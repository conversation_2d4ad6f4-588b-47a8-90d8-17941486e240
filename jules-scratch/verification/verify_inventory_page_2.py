from playwright.sync_api import sync_playwright, expect

def run(playwright):
    browser = playwright.chromium.launch(headless=True)
    context = browser.new_context()
    page = context.new_page()
    page.goto("http://localhost:3000")
    expect(page.get_by_role("heading", name="Seafood Inventory Voice Assistant")).to_be_visible()
    page.get_by_role("button", name="Inventory").click()
    page.screenshot(path="jules-scratch/verification/inventory_page.png")
    browser.close()

with sync_playwright() as playwright:
    run(playwright)