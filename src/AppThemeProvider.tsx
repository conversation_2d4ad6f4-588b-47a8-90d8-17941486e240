import * as React from 'react';
import { CssVarsProvider, useColorScheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import IconButton from '@mui/material/IconButton';
import LightModeIcon from '@mui/icons-material/LightMode';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { theme } from './theme';

function ModeToggle() {
  const { mode, setMode } = useColorScheme();
  return (
    <IconButton
      size="small"
      color="primary"
      onClick={() => setMode(mode === 'dark' ? 'light' : 'dark')}
      aria-label="Toggle color mode"
    >
      {mode === 'dark' ? <LightModeIcon /> : <DarkModeIcon />}
    </IconButton>
  );
}

export const TopbarActions: React.FC<{ userEmail?: string }> = ({ userEmail }) => (
  <Stack direction="row" spacing={1.5} alignItems="center">
    {userEmail && (
      <Typography variant="caption" color="text.secondary" sx={{ display: { xs: 'none', sm: 'block' } }}>
        {userEmail}
      </Typography>
    )}
    <ModeToggle />
  </Stack>
);

export default function AppThemeProvider({ children }: { children: React.ReactNode }) {
  return (
    <CssVarsProvider theme={theme} defaultMode="light">
      <CssBaseline />
      {children}
    </CssVarsProvider>
  );
}
