import { experimental_extendTheme as extendTheme } from '@mui/material/styles';

const berry = {
  primary:   { 50:'#eef2ff',100:'#e0e7ff',200:'#c7d2fe',300:'#a5b4fc',400:'#818cf8',500:'#6366f1',600:'#5458d9',700:'#4648b8',800:'#373996',900:'#2b2f7a' },
  secondary: { 50:'#f2fbff',100:'#def5ff',200:'#bfeaff',300:'#94dbff',400:'#66c7ff',500:'#38b6ff',600:'#2096db',700:'#1c79b2',800:'#195f8f',900:'#154c73' },
  neutral:   { 0:'#ffffff',50:'#f7f7fb',100:'#eef0f4',200:'#e5e7eb',300:'#d3d6de',600:'#556070',800:'#1f2430',900:'#0f131b' },
};

export const theme = extendTheme({
  colorSchemes: {
    light: {
      palette: {
        primary: { main: berry.primary[500] },
        secondary: { main: berry.secondary[500] },
        background: { default: berry.neutral[50], paper: berry.neutral[0] },
        text: { primary: berry.neutral[900], secondary: '#6b7280' },
        divider: 'rgba(15,19,27,0.08)',
      },
    },
    dark: {
      palette: {
        primary: { main: berry.primary[400] },
        secondary: { main: berry.secondary[400] },
        background: { default: '#0b0f16', paper: '#0f131b' },
        text: { primary: '#e5e7eb', secondary: '#98a2b3' },
        divider: 'rgba(255,255,255,0.12)',
      },
    },
  },
  shape: { borderRadius: 12 },
  components: {
    MuiPaper: {
      defaultProps: { variant: 'outlined', elevation: 0 },
      styleOverrides: { root: { borderColor: 'var(--mui-palette-divider)' } },
    },
    MuiAppBar: { styleOverrides: { root: { boxShadow: 'none', borderBottom: '1px solid var(--mui-palette-divider)' } } },
    MuiDrawer: { styleOverrides: { paper: { borderRight: '1px solid var(--mui-palette-divider)' } } },
    MuiDialog: {
      defaultProps: {
        fullWidth: true, maxWidth: 'sm',
        slotProps: {
          backdrop: { sx: { backdropFilter: 'blur(6px)', backgroundColor: 'rgba(0,0,0,.45)' } },
          paper: { sx: { borderRadius: 16 } },
        },
      },
    },
    MuiCssBaseline: {
      styleOverrides: {
        '*:focus-visible': { outline: 'none', boxShadow: '0 0 0 3px rgba(99,102,241,.35)' },
      },
    },
  },
});