import * as React from 'react';
import {
  AppBar,
  Box,
  Drawer,
  IconButton,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  useMediaQuery,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { useTheme } from '@mui/material/styles';

export type NavItem = {
  label: string;
  onClick?: () => void;
  active?: boolean;
  icon?: React.ReactNode;
};

interface DashboardLayoutProps {
  title: string;
  subtitle?: string;
  nav: NavItem[];
  children: React.ReactNode;
  toolbarActions?: React.ReactNode;
  primaryAction?: React.ReactNode;
}

const NAV_WIDTH = 264;

export default function DashboardLayout({
  title,
  subtitle,
  nav,
  children,
  toolbarActions,
  primaryAction,
}: DashboardLayoutProps) {
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
  const [open, setOpen] = React.useState(isDesktop);

  React.useEffect(() => {
    setOpen(isDesktop);
  }, [isDesktop]);

  const drawerWidth = open ? NAV_WIDTH : 72;

  return (
    <Box sx={{ display: 'flex', bgcolor: 'background.default', minHeight: '100dvh' }}>
      <Drawer
        variant={isDesktop ? 'permanent' : 'temporary'}
        open={open}
        onClose={() => setOpen(false)}
        ModalProps={{ keepMounted: true }}
        sx={{
          width: drawerWidth,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
            overflowX: 'hidden',
            transition: theme.transitions.create('width'),
          },
        }}
      >
        <Toolbar sx={{ gap: 1, px: 1.5 }}>
          <IconButton onClick={() => setOpen((prev) => !prev)} aria-label="Toggle navigation width">
            <MenuIcon />
          </IconButton>
          {open && (
            <Typography variant="subtitle1" fontWeight={800}>
              HACCP Helper
            </Typography>
          )}
        </Toolbar>
        <List sx={{ px: 0.5 }}>
          {nav.map((item) => (
            <ListItemButton
              key={item.label}
              onClick={() => {
                item.onClick?.();
                if (!isDesktop) {
                  setOpen(false);
                }
              }}
              selected={item.active}
              sx={{ borderRadius: 2, mb: 0.5 }}
            >
              {item.icon && (
                <ListItemIcon sx={{ minWidth: open ? 36 : 48, justifyContent: open ? 'flex-start' : 'center' }}>
                  {item.icon}
                </ListItemIcon>
              )}
              <ListItemText primary={item.label} primaryTypographyProps={{ noWrap: true }} />
            </ListItemButton>
          ))}
        </List>
      </Drawer>
      <Box
        sx={{
          flex: 1,
          minWidth: 0,
        }}
      >
        <AppBar
          color="inherit"
          position="sticky"
          elevation={0}
          sx={{
            borderBottom: '1px solid',
            borderColor: 'divider',
            bgcolor: 'background.paper',
          }}
        >
          <Toolbar sx={{ gap: 2, alignItems: 'center' }}>
            {!isDesktop && (
              <IconButton onClick={() => setOpen(true)} aria-label="Open navigation drawer">
                <MenuIcon />
              </IconButton>
            )}
            <Box>
              <Typography variant="h5" fontWeight={700}>
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="body2" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
            </Box>
            {primaryAction}
            <Box sx={{ flex: 1 }} />
            {toolbarActions}
          </Toolbar>
        </AppBar>
        <Box component="main" sx={{ p: { xs: 2, md: 3 }, display: 'flex', flexDirection: 'column', gap: 3 }}>
          {children}
        </Box>
      </Box>
    </Box>
  );
}
