import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';
import { getAuth, GoogleAuthProvider } from 'firebase/auth';

const firebaseConfig = {
  apiKey: "AIzaSyANh8YdqDxMf9Trf9p217n83agVH3-3uxg",
  authDomain: "seafood-inventory-voice.firebaseapp.com",
  projectId: "seafood-inventory-voice",
  storageBucket: "seafood-inventory-voice.appspot.com",
  messagingSenderId: "722364018053",
  appId: "1:722364018053:web:8cc4f0a7b31697fce5e8ec",
  measurementId: "G-BNSQR53GR8"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize services
export const db = getFirestore(app);
export const storage = getStorage(app);
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();