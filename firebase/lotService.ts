import {
  collection,
  addDoc,
  serverTimestamp,
  onSnapshot,
  query,
  orderBy,
  doc,
  updateDoc,
  where,
  getDocs,
} from 'firebase/firestore';
import { db } from './config';
import { Lot, LotMovement } from '../types';
import { docToPlainObject } from '../utils/firestoreUtils';

const lotsCollection = collection(db, 'lots');
const movementsCollection = collection(db, 'lotMovements');

export const onLotsUpdate = (callback: (lots: Lot[]) => void) => {
  const q = query(lotsCollection, orderBy('createdAt', 'desc'));
  return onSnapshot(q, (snapshot) => {
    const lots = snapshot.docs.map(doc => docToPlainObject<Lot>(doc));
    callback(lots);
  });
};

export const onLotMovementsUpdate = (lotId: string, callback: (movements: LotMovement[]) => void) => {
  const q = query(movementsCollection, where('lotId', '==', lotId), orderBy('timestamp', 'desc'));
  return onSnapshot(q, (snapshot) => {
    const movements = snapshot.docs.map(doc => docToPlainObject<LotMovement>(doc));
    callback(movements);
  });
};

export const onAllMovementsUpdate = (callback: (movements: LotMovement[]) => void) => {
    const q = query(movementsCollection, orderBy('timestamp', 'desc'));
    return onSnapshot(q, (snapshot) => {
        const movements = snapshot.docs.map(doc => docToPlainObject<LotMovement>(doc));
        callback(movements);
    });
};

export const addLot = async (lotData: Omit<Lot, 'id' | 'createdAt' | 'updatedAt'>) => {
  try {
    await addDoc(lotsCollection, {
      ...lotData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error("Error adding lot: ", error);
    throw new Error('Failed to add lot.');
  }
};

export const addLotMovement = async (movementData: Omit<LotMovement, 'id' | 'timestamp'>) => {
  try {
    // Add the new movement
    await addDoc(movementsCollection, {
      ...movementData,
      timestamp: serverTimestamp(),
    });

    // Also update the parent lot's `updatedAt` timestamp
    const lotQuery = query(lotsCollection, where('lotId', '==', movementData.lotId));
    // In a real app, you'd want to ensure lotId is unique or handle multiple results.
    // For now, we'll assume it's unique and update the first one found.
    // A better approach would be to store the firestore doc ID of the lot.
    const lotSnapshot = await getDocs(lotQuery);
    if (!lotSnapshot.empty) {
        const lotDocRef = lotSnapshot.docs[0].ref;
        await updateDoc(lotDocRef, {
            updatedAt: serverTimestamp(),
        });
    }

  } catch (error) {
    console.error("Error adding lot movement: ", error);
    throw new Error('Failed to add lot movement.');
  }
};