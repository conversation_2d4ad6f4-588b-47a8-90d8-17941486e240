import { QueryDocumentSnapshot, DocumentData } from 'firebase/firestore';

/**
 * Converts a Firestore document snapshot to a plain JavaScript object.
 * It includes the document ID and converts any Firestore Timestamp fields to ISO date strings.
 * @param doc The Firestore document snapshot.
 * @returns A plain JavaScript object representation of the document.
 */
export const docToPlainObject = <T>(doc: QueryDocumentSnapshot<DocumentData>): T => {
  const data = doc.data();
  const plainObject: { [key: string]: any } = { id: doc.id };

  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const value = data[key];
      // Check if it's a Firestore Timestamp and convert it
      if (value && typeof value.toDate === 'function') {
        plainObject[key] = value.toDate().toISOString();
      } else {
        plainObject[key] = value;
      }
    }
  }
  return plainObject as T;
};