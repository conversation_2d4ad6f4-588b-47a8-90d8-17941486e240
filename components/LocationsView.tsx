import React, { useEffect, useMemo, useState } from 'react';
import {
    Alert,
    Box,
    Button,
    CircularProgress,
    Grid,
    Paper,
    Stack,
    TextField,
    Typography,
} from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { db } from '../firebase/config';
import { FreezerLayoutModal } from './FreezerLayoutModal';
import { Location, HACCPEvent } from '../types';
import { CubeIcon, EyeIcon, MapPinIcon, PlusIcon } from './IconComponents';
import { docToPlainObject } from '../utils/firestoreUtils';

interface LocationsViewProps {
    events: HACCPEvent[];
}

export const LocationsView: React.FC<LocationsViewProps> = ({ events }) => {
    const theme = useTheme();
    const [locations, setLocations] = useState<Location[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [isLayoutModalOpen, setIsLayoutModalOpen] = useState(false);
    const [selectedLocationForLayout, setSelectedLocationForLayout] = useState<Location | null>(null);

    const [editingId, setEditingId] = useState<string | null>(null);
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');

    const inventoryByLocation = useMemo(() => {
        const inventoryMap: Record<string, Record<string, number>> = {};

        [...events]
            .sort((a, b) => {
                const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
                const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
                return timeA - timeB;
            })
            .forEach((event) => {
                if (!event.product) return;
                const product = event.product;
                const quantity = event.quantity || 0;

                const ensureLocation = (locationName: string) => {
                    if (!inventoryMap[locationName]) {
                        inventoryMap[locationName] = {};
                    }
                    if (!inventoryMap[locationName][product]) {
                        inventoryMap[locationName][product] = 0;
                    }
                };

                switch (event.eventType) {
                    case 'receiving':
                        if (event.location) {
                            ensureLocation(event.location);
                            inventoryMap[event.location][product] += quantity;
                        }
                        break;
                    case 'sales':
                    case 'disposal':
                        if (event.location && inventoryMap[event.location]?.[product]) {
                            inventoryMap[event.location][product] -= quantity;
                        }
                        break;
                    case 'relocation':
                        if (event.fromLocation && inventoryMap[event.fromLocation]?.[product]) {
                            inventoryMap[event.fromLocation][product] -= quantity;
                        }
                        if (event.location) {
                            ensureLocation(event.location);
                            inventoryMap[event.location][product] += quantity;
                        }
                        break;
                    default:
                        break;
                }
            });

        Object.keys(inventoryMap).forEach((loc) => {
            Object.keys(inventoryMap[loc]).forEach((product) => {
                if (inventoryMap[loc][product] <= 0.01) {
                    delete inventoryMap[loc][product];
                }
            });
        });

        return inventoryMap;
    }, [events]);

    useEffect(() => {
        try {
            const locationsQuery = query(collection(db, 'locations'), orderBy('name'));
            const unsubscribe = onSnapshot(
                locationsQuery,
                (snapshot: QuerySnapshot<DocumentData>) => {
                    const list = snapshot.docs.map((docSnapshot) => docToPlainObject<Location>(docSnapshot));
                    setLocations(list);
                    setLoading(false);
                },
                (snapshotError) => {
                    console.error(snapshotError);
                    setError('Failed to fetch locations.');
                    setLoading(false);
                },
            );
            return () => unsubscribe();
        } catch (initializationError) {
            console.error(initializationError);
            setError('Failed to initialize Firebase for locations.');
            setLoading(false);
        }
    }, []);

    const resetForm = () => {
        setEditingId(null);
        setName('');
        setDescription('');
        setIsFormOpen(false);
    };

    const handleEdit = (location: Location) => {
        setEditingId(location.id);
        setName(location.name);
        setDescription(location.description || '');
        setIsFormOpen(true);
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        if (!name.trim()) {
            alert('Location name is required.');
            return;
        }

        const locationData: Partial<Location> = {
            name: name.trim(),
            description: description.trim() || undefined,
        };

        try {
            if (editingId) {
                const locationRef = doc(db, 'locations', editingId);
                await updateDoc(locationRef, locationData);
            } else {
                await addDoc(collection(db, 'locations'), locationData);
            }
            resetForm();
        } catch (submitError) {
            console.error(submitError);
            alert('Failed to save location.');
        }
    };

    const iconContainerSx = {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        '& svg': {
            width: 20,
            height: 20,
        },
    } as const;

    const openLayoutModal = (location: Location) => {
        setSelectedLocationForLayout(location);
        setIsLayoutModalOpen(true);
    };

    return (
        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Paper variant="outlined" sx={{ p: { xs: 3, md: 4 }, borderRadius: 3 }}>
                <Stack
                    direction={{ xs: 'column', sm: 'row' }}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems={{ xs: 'flex-start', sm: 'center' }}
                >
                    <Stack direction="row" spacing={2} alignItems="center">
                        <Box
                            sx={{
                                ...iconContainerSx,
                                width: 48,
                                height: 48,
                                borderRadius: 2,
                                bgcolor: alpha(theme.palette.info.main, theme.palette.mode === 'dark' ? 0.35 : 0.12),
                                color: theme.palette.info.main,
                            }}
                        >
                            <MapPinIcon />
                        </Box>
                        <Typography variant="h4" fontWeight={700} color="text.primary">
                            Location Directory
                        </Typography>
                    </Stack>
                    <Button
                        variant={isFormOpen ? 'outlined' : 'contained'}
                        color="info"
                        startIcon={
                            <Box sx={{ ...iconContainerSx, color: 'inherit' }}>
                                <PlusIcon />
                            </Box>
                        }
                        onClick={() => setIsFormOpen((prev) => !prev)}
                        sx={{ textTransform: 'none' }}
                    >
                        {isFormOpen ? 'Close Form' : 'Add Location'}
                    </Button>
                </Stack>

                {isFormOpen && (
                    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 4 }}>
                        <Grid container spacing={2.5}>
                            <Grid item xs={12} md={6}>
                                <TextField label="Location Name" fullWidth required value={name} onChange={(event) => setName(event.target.value)} />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    label="Description"
                                    fullWidth
                                    multiline
                                    minRows={3}
                                    value={description}
                                    onChange={(event) => setDescription(event.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <Stack direction="row" spacing={2} justifyContent="flex-end">
                                    <Button variant="outlined" color="inherit" onClick={resetForm} sx={{ textTransform: 'none' }}>
                                        Cancel
                                    </Button>
                                    <Button type="submit" variant="contained" color="info" sx={{ textTransform: 'none' }}>
                                        {editingId ? 'Update Location' : 'Save Location'}
                                    </Button>
                                </Stack>
                            </Grid>
                        </Grid>
                    </Box>
                )}
            </Paper>

            {loading ? (
                <Box sx={{ display: 'grid', placeItems: 'center', py: 6 }}>
                    <CircularProgress size={24} color="info" />
                </Box>
            ) : error ? (
                <Alert severity="error">{error}</Alert>
            ) : (
                <Grid container spacing={3}>
                    {locations.length === 0 ? (
                        <Grid item xs={12}>
                            <Paper variant="outlined" sx={{ p: 4, textAlign: 'center', borderRadius: 2 }}>
                                <Typography variant="body1" color="text.secondary">
                                    No locations configured yet.
                                </Typography>
                            </Paper>
                        </Grid>
                    ) : (
                        locations.map((location) => {
                            const inventory = inventoryByLocation[location.name] || {};
                            const productEntries = Object.entries(inventory);
                            const productCount = productEntries.length;

                            return (
                                <Grid item xs={12} md={6} key={location.id}>
                                    <Paper
                                        variant="outlined"
                                        sx={{
                                            p: 3,
                                            borderRadius: 2,
                                            height: '100%',
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: 2,
                                            bgcolor: alpha(theme.palette.background.paper, theme.palette.mode === 'dark' ? 0.2 : 0.04),
                                        }}
                                    >
                                        <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                                            <Box>
                                                <Typography variant="h6" fontWeight={700} color="text.primary">
                                                    {location.name}
                                                </Typography>
                                                {location.description && (
                                                    <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                                                        {location.description}
                                                    </Typography>
                                                )}
                                            </Box>
                                            <Stack direction="row" spacing={1}>
                                                <Button
                                                    size="small"
                                                    variant="outlined"
                                                    color="info"
                                                    onClick={() => openLayoutModal(location)}
                                                    startIcon={
                                                        <Box sx={{ ...iconContainerSx, color: 'inherit' }}>
                                                            <EyeIcon />
                                                        </Box>
                                                    }
                                                    sx={{ textTransform: 'none' }}
                                                >
                                                    View Layout
                                                </Button>
                                                <Button
                                                    size="small"
                                                    variant="text"
                                                    color="info"
                                                    onClick={() => handleEdit(location)}
                                                    sx={{ textTransform: 'none', fontWeight: 600 }}
                                                >
                                                    Edit
                                                </Button>
                                            </Stack>
                                        </Stack>
                                        <Box sx={{ pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
                                            <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
                                                <Box sx={{ ...iconContainerSx, color: theme.palette.text.secondary }}>
                                                    <CubeIcon />
                                                </Box>
                                                <Typography variant="subtitle2" color="text.secondary" fontWeight={600}>
                                                    Current Inventory
                                                </Typography>
                                            </Stack>
                                            {productCount > 0 ? (
                                                <Grid container spacing={1}>
                                                    {productEntries.map(([product, quantity]) => (
                                                        <Grid item xs={12} sm={6} key={product}>
                                                            <Stack direction="row" justifyContent="space-between" spacing={1}>
                                                                <Typography
                                                                    variant="body2"
                                                                    color="text.secondary"
                                                                    sx={{ flexGrow: 1, pr: 1, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
                                                                    title={product}
                                                                >
                                                                    {product}
                                                                </Typography>
                                                                <Typography variant="body2" fontWeight={700} color="text.primary">
                                                                    {quantity.toFixed(2)} lbs
                                                                </Typography>
                                                            </Stack>
                                                        </Grid>
                                                    ))}
                                                </Grid>
                                            ) : (
                                                <Typography variant="body2" color="text.secondary">
                                                    No inventory tracked at this location.
                                                </Typography>
                                            )}
                                        </Box>
                                    </Paper>
                                </Grid>
                            );
                        })
                    )}
                </Grid>
            )}

            {isLayoutModalOpen && selectedLocationForLayout && (
                <FreezerLayoutModal
                    isOpen={isLayoutModalOpen}
                    onClose={() => setIsLayoutModalOpen(false)}
                    location={selectedLocationForLayout}
                    inventory={inventoryByLocation[selectedLocationForLayout.name] || {}}
                />
            )}
        </Box>
    );
};

LocationsView.displayName = 'LocationsView';
