import React, { useMemo, useState } from 'react';
import {
    <PERSON>,
    Chip,
    Dialog,
    DialogContent,
    DialogTitle,
    IconButton,
    Paper,
    Stack,
    Tooltip,
    Typography,
} from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { HACCPEvent } from '../types';
import {
    ArchiveBoxIcon,
    CalendarIcon,
    ClockIcon,
    PackageIcon,
    PlusIcon,
    ShoppingCartIcon,
    SparklesIcon,
    ThermometerIcon,
    TrashIcon,
    TruckIcon,
    UserCircleIcon,
    ClipboardIcon,
    XIcon,
} from './IconComponents';

interface CalendarViewProps {
    events: HACCPEvent[];
    onAddNewEvent: (date: string) => void;
}

type PaletteKey = 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info' | 'grey';

const EVENT_TYPE_CONFIG: Record<string, { icon: React.ReactNode; label: string; paletteKey: PaletteKey }> = {
    receiving: { icon: <PackageIcon />, label: 'Receiving', paletteKey: 'primary' },
    sales: { icon: <ShoppingCartIcon />, label: 'Sales', paletteKey: 'success' },
    disposal: { icon: <TrashIcon />, label: 'Disposal', paletteKey: 'error' },
    're-sealing': { icon: <ClipboardIcon />, label: 'Re-sealing', paletteKey: 'secondary' },
    relocation: { icon: <TruckIcon />, label: 'Relocation', paletteKey: 'info' },
    sanitation: { icon: <SparklesIcon />, label: 'Sanitation', paletteKey: 'success' },
    'thermometer-calibration': { icon: <ThermometerIcon />, label: 'Calibration', paletteKey: 'warning' },
    inventory: { icon: <ArchiveBoxIcon />, label: 'Inventory', paletteKey: 'primary' },
    'employee-training': { icon: <UserCircleIcon />, label: 'Training', paletteKey: 'secondary' },
};

const WEEK_DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const iconBoxSx = {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    '& svg': {
        width: 16,
        height: 16,
    },
} as const;

const EventTypeDisplay: React.FC<{ eventType: string }> = ({ eventType }) => {
    const theme = useTheme();
    const config = EVENT_TYPE_CONFIG[eventType] || {
        icon: <XIcon />,
        label: eventType,
        paletteKey: 'grey' as PaletteKey,
    };

    let chipBg: string;
    let chipText: string;
    let chipIcon: string;

    if (config.paletteKey === 'grey') {
        chipBg = alpha(theme.palette.grey[500], theme.palette.mode === 'dark' ? 0.25 : 0.16);
        chipText = theme.palette.mode === 'dark' ? theme.palette.grey[100] : theme.palette.grey[800];
        chipIcon = theme.palette.grey[600];
    } else {
        const paletteColor = theme.palette[config.paletteKey];
        chipBg = alpha(paletteColor.main, theme.palette.mode === 'dark' ? 0.3 : 0.16);
        chipText = theme.palette.mode === 'dark' ? paletteColor.light : paletteColor.dark;
        chipIcon = paletteColor.main;
    }

    return (
        <Chip
            size="small"
            icon={config.icon ? <Box sx={{ ...iconBoxSx, color: chipIcon }}>{config.icon}</Box> : undefined}
            label={config.label}
            sx={{
                backgroundColor: chipBg,
                color: chipText,
                fontWeight: 600,
                '& .MuiChip-icon': {
                    color: chipIcon,
                },
            }}
        />
    );
};

const InfoRow: React.FC<{ label: string; value: React.ReactNode }> = ({ label, value }) => (
    <Typography variant="body2" color="text.secondary">
        <Typography component="span" variant="body2" sx={{ fontWeight: 600, color: 'text.primary' }}>
            {label}:{' '}
        </Typography>
        {value}
    </Typography>
);

const EventModal: React.FC<{ date: string; events: HACCPEvent[]; onClose: () => void }> = ({ date, events, onClose }) => {
    const theme = useTheme();
    const formattedDate = new Date(`${date}T00:00:00`).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    });

    return (
        <Dialog open onClose={onClose} maxWidth="md" fullWidth>
            <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', pr: 1 }}>
                <Typography variant="h6">{formattedDate}</Typography>
                <IconButton onClick={onClose} edge="end" aria-label="Close event details dialog">
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent dividers sx={{ p: 3 }}>
                {events.length > 0 ? (
                    <Stack spacing={2.5}>
                        {events
                            .slice()
                            .sort((a, b) => a.time.localeCompare(b.time))
                            .map((event) => {
                                const showProductDetails = ['receiving', 'sales', 'disposal', 're-sealing', 'inventory', 'relocation'].includes(
                                    event.eventType,
                                );

                                return (
                                    <Paper
                                        key={event.id}
                                        variant="outlined"
                                        sx={{
                                            p: 2.5,
                                            borderRadius: 2,
                                            backgroundColor: alpha(theme.palette.background.paper, theme.palette.mode === 'dark' ? 0.2 : 0.04),
                                        }}
                                    >
                                        <Stack spacing={1.5}>
                                            <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                                                <EventTypeDisplay eventType={event.eventType} />
                                                <Stack direction="row" spacing={1} alignItems="center" color="text.secondary">
                                                    <Box sx={{ ...iconBoxSx, '& svg': { width: 16, height: 16 } }}>
                                                        <ClockIcon />
                                                    </Box>
                                                    <Typography variant="body2">{event.time}</Typography>
                                                </Stack>
                                            </Stack>

                                            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2.5} alignItems={{ xs: 'stretch', sm: 'flex-start' }}>
                                                {event.imageUrl && (
                                                    <Box
                                                        component="img"
                                                        src={event.imageUrl}
                                                        alt={event.imageDescription || 'Event image'}
                                                        sx={{
                                                            width: 120,
                                                            height: 120,
                                                            borderRadius: 2,
                                                            objectFit: 'cover',
                                                            boxShadow: theme.shadows[1],
                                                        }}
                                                    />
                                                )}

                                                <Stack spacing={1} flexGrow={1}>
                                                    {showProductDetails && (
                                                        <>
                                                            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                                                                {[event.product, event.productForm].filter(Boolean).join(' - ')}
                                                            </Typography>
                                                            <Box
                                                                sx={{
                                                                    display: 'grid',
                                                                    gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, minmax(0, 1fr))' },
                                                                    gap: 1,
                                                                }}
                                                            >
                                                                {typeof event.quantity === 'number' && (
                                                                    <InfoRow label="Quantity" value={`${event.quantity.toFixed(2)} ${event.unit}`} />
                                                                )}
                                                                {event.batchNumber && (
                                                                    <InfoRow
                                                                        label="Batch"
                                                                        value={
                                                                            <Typography component="span" variant="body2" sx={{ fontFamily: 'monospace' }}>
                                                                                {event.batchNumber}
                                                                            </Typography>
                                                                        }
                                                                    />
                                                                )}
                                                                {event.supplier && <InfoRow label="Supplier" value={event.supplier} />}
                                                                {event.temperature && <InfoRow label="Temp" value={`${event.temperature}°F`} />}
                                                                {event.fromLocation && <InfoRow label="From" value={event.fromLocation} />}
                                                                {event.location && (
                                                                    <InfoRow
                                                                        label={event.eventType === 'relocation' ? 'To' : 'Location'}
                                                                        value={event.location}
                                                                    />
                                                                )}
                                                            </Box>
                                                        </>
                                                    )}

                                                    {event.eventType === 'employee-training' && (
                                                        <Box
                                                            sx={{
                                                                display: 'grid',
                                                                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, minmax(0, 1fr))' },
                                                                gap: 1,
                                                            }}
                                                        >
                                                            {event.employeeName && <InfoRow label="Employee" value={event.employeeName} />}
                                                            {event.trainingTopic && <InfoRow label="Topic" value={event.trainingTopic} />}
                                                        </Box>
                                                    )}

                                                    {event.eventType === 'sanitation' && (
                                                        <Box
                                                            sx={{
                                                                display: 'grid',
                                                                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, minmax(0, 1fr))' },
                                                                gap: 1,
                                                            }}
                                                        >
                                                            {event.areaCleaned && <InfoRow label="Area Cleaned" value={event.areaCleaned} />}
                                                            {event.sanitizerUsed && <InfoRow label="Sanitizer Used" value={event.sanitizerUsed} />}
                                                        </Box>
                                                    )}

                                                    {event.eventType === 'thermometer-calibration' && (
                                                        <Box
                                                            sx={{
                                                                display: 'grid',
                                                                gridTemplateColumns: { xs: '1fr', sm: 'repeat(2, minmax(0, 1fr))' },
                                                                gap: 1,
                                                            }}
                                                        >
                                                            {event.thermometerId && <InfoRow label="Thermometer ID" value={event.thermometerId} />}
                                                            {event.result && (
                                                                <InfoRow
                                                                    label="Result"
                                                                    value={
                                                                        <Typography
                                                                            component="span"
                                                                            variant="body2"
                                                                            sx={{
                                                                                fontWeight: 700,
                                                                                color:
                                                                                    event.result === 'pass'
                                                                                        ? theme.palette.success.main
                                                                                        : theme.palette.error.main,
                                                                            }}
                                                                        >
                                                                            {event.result.toUpperCase()}
                                                                        </Typography>
                                                                    }
                                                                />
                                                            )}
                                                            {event.calibrationMethod && <InfoRow label="Method" value={event.calibrationMethod} />}
                                                        </Box>
                                                    )}
                                                </Stack>
                                            </Stack>

                                            <Stack spacing={1} sx={{ pt: 1, borderTop: `1px solid ${theme.palette.divider}` }}>
                                                {event.imageDescription && (
                                                    <InfoRow label="AI Analysis" value={event.imageDescription} />
                                                )}
                                                {event.correctiveAction && (
                                                    <InfoRow label="Corrective Action" value={event.correctiveAction} />
                                                )}
                                                {event.notes && <InfoRow label="Notes" value={event.notes} />}
                                            </Stack>
                                        </Stack>
                                    </Paper>
                                );
                            })}
                    </Stack>
                ) : (
                    <Typography textAlign="center" color="text.secondary">
                        No events found for this date.
                    </Typography>
                )}
            </DialogContent>
        </Dialog>
    );
};

export const CalendarView: React.FC<CalendarViewProps> = ({ events, onAddNewEvent }) => {
    const theme = useTheme();
    const [currentDate, setCurrentDate] = useState(new Date());
    const [selectedDate, setSelectedDate] = useState<string | null>(null);

    const eventsByDate = useMemo(() => {
        const map = new Map<string, HACCPEvent[]>();
        events.forEach((event) => {
            const dateStr = event.date;
            if (!map.has(dateStr)) {
                map.set(dateStr, []);
            }
            map.get(dateStr)!.push(event);
        });
        return map;
    }, [events]);

    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const firstDayOfMonth = new Date(year, month, 1).getDay();

    const changeMonth = (delta: number) => {
        setCurrentDate((prev) => {
            const newDate = new Date(prev);
            newDate.setMonth(newDate.getMonth() + delta);
            return newDate;
        });
    };

    const calendarCells: React.ReactNode[] = [];
    for (let i = 0; i < firstDayOfMonth; i += 1) {
        calendarCells.push(<Box key={`empty-${i}`} sx={{ borderRight: `1px solid ${theme.palette.divider}`, borderBottom: `1px solid ${theme.palette.divider}`, minHeight: { xs: 72, sm: 88 } }} />);
    }

    for (let day = 1; day <= daysInMonth; day += 1) {
        const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const dayEvents = eventsByDate.get(dateStr) || [];
        const isToday = new Date().toDateString() === new Date(year, month, day).toDateString();

        const indicatorColors = [theme.palette.primary.main, theme.palette.success.main, theme.palette.error.main];

        calendarCells.push(
            <Box
                key={day}
                sx={{
                    position: 'relative',
                    p: 1.5,
                    borderRight: `1px solid ${theme.palette.divider}`,
                    borderBottom: `1px solid ${theme.palette.divider}`,
                    minHeight: { xs: 88, sm: 104 },
                    cursor: dayEvents.length > 0 ? 'pointer' : 'default',
                    transition: theme.transitions.create('background-color'),
                    '&:hover': {
                        backgroundColor:
                            dayEvents.length > 0
                                ? alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.18 : 0.08)
                                : undefined,
                    },
                    '&:hover .calendar-add-button': {
                        opacity: 1,
                    },
                }}
                onClick={() => {
                    if (dayEvents.length > 0) {
                        setSelectedDate(dateStr);
                    }
                }}
            >
                <Typography
                    component="time"
                    variant="body2"
                    sx={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 28,
                        height: 28,
                        borderRadius: '50%',
                        fontWeight: 600,
                        color: isToday ? theme.palette.primary.contrastText : theme.palette.text.secondary,
                        backgroundColor: isToday ? theme.palette.primary.main : 'transparent',
                    }}
                >
                    {day}
                </Typography>

                <IconButton
                    size="small"
                    className="calendar-add-button"
                    onClick={(event) => {
                        event.stopPropagation();
                        onAddNewEvent(dateStr);
                    }}
                    sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        width: 32,
                        height: 32,
                        bgcolor: theme.palette.primary.main,
                        color: theme.palette.primary.contrastText,
                        boxShadow: theme.shadows[2],
                        opacity: 0,
                        transition: theme.transitions.create(['opacity', 'background-color', 'box-shadow']),
                        '&:hover': {
                            bgcolor: theme.palette.primary.dark,
                            boxShadow: theme.shadows[4],
                        },
                        '&:focus-visible': {
                            opacity: 1,
                        },
                    }}
                    aria-label="Add new event for this date"
                >
                    <Box sx={{ ...iconBoxSx, '& svg': { width: 18, height: 18 } }}>
                        <PlusIcon />
                    </Box>
                </IconButton>

                {dayEvents.length > 0 && (
                    <Stack
                        direction="row"
                        spacing={0.5}
                        sx={{ position: 'absolute', bottom: 10, left: 12 }}
                        alignItems="center"
                    >
                        {indicatorColors.slice(0, Math.min(dayEvents.length, 3)).map((color, index) => (
                            <Tooltip key={color + index} title={index === 0 ? 'Event' : 'Multiple Events'} placement="top">
                                <Box
                                    component="span"
                                    sx={{
                                        width: 8,
                                        height: 8,
                                        borderRadius: '50%',
                                        bgcolor: color,
                                    }}
                                />
                            </Tooltip>
                        ))}
                    </Stack>
                )}
            </Box>,
        );
    }

    return (
        <Paper sx={{ borderRadius: 3, p: { xs: 3, sm: 4 }, width: '100%', boxShadow: theme.shadows[6] }}>
            <Box
                component="header"
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 3,
                    pb: 2,
                    borderBottom: `1px solid ${theme.palette.divider}`,
                }}
            >
                <Stack direction="row" spacing={2} alignItems="center">
                    <Box
                        sx={{
                            ...iconBoxSx,
                            width: 40,
                            height: 40,
                            borderRadius: 2,
                            bgcolor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.3 : 0.12),
                            color: theme.palette.primary.main,
                        }}
                    >
                        <CalendarIcon />
                    </Box>
                    <Typography variant="h5" component="h1" sx={{ fontWeight: 700 }}>
                        Event Calendar
                    </Typography>
                </Stack>
                <Stack direction="row" spacing={1} alignItems="center">
                    <IconButton onClick={() => changeMonth(-1)} aria-label="Previous month">
                        <ChevronLeftIcon />
                    </IconButton>
                    <Typography variant="subtitle1" sx={{ minWidth: 160, textAlign: 'center', fontWeight: 600 }}>
                        {currentDate.toLocaleString('default', { month: 'long', year: 'numeric' })}
                    </Typography>
                    <IconButton onClick={() => changeMonth(1)} aria-label="Next month">
                        <ChevronRightIcon />
                    </IconButton>
                </Stack>
            </Box>

            <Paper variant="outlined" sx={{ borderRadius: 2, overflow: 'hidden' }}>
                <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', bgcolor: alpha(theme.palette.background.default, theme.palette.mode === 'dark' ? 0.3 : 0.04) }}>
                    {WEEK_DAYS.map((day) => (
                        <Box
                            key={day}
                            sx={{
                                textAlign: 'center',
                                py: 1.5,
                                borderRight: `1px solid ${theme.palette.divider}`,
                                borderBottom: `1px solid ${theme.palette.divider}`,
                                '&:nth-of-type(7n)': {
                                    borderRight: 'none',
                                },
                            }}
                        >
                            <Typography variant="caption" sx={{ fontWeight: 700, color: 'text.secondary' }}>
                                {day}
                            </Typography>
                        </Box>
                    ))}
                </Box>
                <Box
                    sx={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(7, 1fr)',
                        '& > :nth-of-type(7n)': {
                            borderRight: 'none',
                        },
                    }}
                >
                    {calendarCells}
                </Box>
            </Paper>

            {selectedDate && (
                <EventModal
                    date={selectedDate}
                    events={eventsByDate.get(selectedDate) || []}
                    onClose={() => setSelectedDate(null)}
                />
            )}
        </Paper>
    );
};

CalendarView.displayName = 'CalendarView';
