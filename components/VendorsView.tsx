import React, { useEffect, useState } from 'react';
import {
    Alert,
    Avatar,
    Box,
    Button,
    CircularProgress,
    Grid,
    Paper,
    Stack,
    TextField,
    Typography,
} from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { db } from '../firebase/config';
import { Vendor } from '../types';
import { BuildingStorefrontIcon, PlusIcon, PhotoIcon } from './IconComponents';
import { uploadImage } from '../utils/imageUtils';
import { docToPlainObject } from '../utils/firestoreUtils';

export const VendorsView: React.FC = () => {
    const theme = useTheme();
    const [vendors, setVendors] = useState<Vendor[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isFormOpen, setIsFormOpen] = useState(false);

    const [editingId, setEditingId] = useState<string | null>(null);
    const [name, setName] = useState('');
    const [contactPerson, setContactPerson] = useState('');
    const [phone, setPhone] = useState('');
    const [email, setEmail] = useState('');
    const [address, setAddress] = useState('');
    const [notes, setNotes] = useState('');
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);
    const [productsCarried, setProductsCarried] = useState('');
    const [sourcingLeadTimeDays, setSourcingLeadTimeDays] = useState('');

    useEffect(() => {
        try {
            const vendorQuery = query(collection(db, 'vendors'), orderBy('name'));
            const unsubscribe = onSnapshot(
                vendorQuery,
                (querySnapshot: QuerySnapshot<DocumentData>) => {
                    const vendorsList: Vendor[] = querySnapshot.docs.map((snapshot) =>
                        docToPlainObject<Vendor>(snapshot),
                    );
                    setVendors(vendorsList);
                    setLoading(false);
                },
                (snapshotError) => {
                    console.error(snapshotError);
                    setError('Failed to fetch vendors.');
                    setLoading(false);
                },
            );
            return () => unsubscribe();
        } catch (setupError) {
            console.error(setupError);
            setError('Failed to initialize Firebase for vendors.');
            setLoading(false);
        }
    }, []);

    const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            const file = event.target.files[0];
            setImageFile(file);
            setImagePreview(URL.createObjectURL(file));
        }
    };

    const resetForm = () => {
        setEditingId(null);
        setName('');
        setContactPerson('');
        setPhone('');
        setEmail('');
        setAddress('');
        setNotes('');
        setImageFile(null);
        setImagePreview(null);
        setProductsCarried('');
        setSourcingLeadTimeDays('');
        setIsFormOpen(false);
    };

    const handleEdit = (vendor: Vendor) => {
        setEditingId(vendor.id);
        setName(vendor.name);
        setContactPerson(vendor.contactPerson || '');
        setPhone(vendor.phone || '');
        setEmail(vendor.email || '');
        setAddress(vendor.address || '');
        setNotes(vendor.notes || '');
        setImagePreview(vendor.imageUrl || null);
        setProductsCarried(vendor.productsCarried?.join('\n') || '');
        setSourcingLeadTimeDays(vendor.sourcingLeadTimeDays?.toString() || '');
        setImageFile(null);
        setIsFormOpen(true);
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        if (!name.trim()) {
            alert('Vendor name is required.');
            return;
        }

        const productsArray = productsCarried
            .split('\n')
            .map((product) => product.trim())
            .filter(Boolean);

        const vendorData: Partial<Vendor> = {
            name: name.trim(),
            contactPerson: contactPerson.trim() || undefined,
            phone: phone.trim() || undefined,
            email: email.trim() || undefined,
            address: address.trim() || undefined,
            notes: notes.trim() || undefined,
            productsCarried: productsArray,
            sourcingLeadTimeDays: Number.parseInt(sourcingLeadTimeDays, 10) || undefined,
        };

        if (imageFile) {
            try {
                const imageUrl = await uploadImage(imageFile, `vendors/${Date.now()}_${imageFile.name}`);
                vendorData.imageUrl = imageUrl;
            } catch (uploadError) {
                console.error(uploadError);
                alert('Failed to upload image.');
                return;
            }
        } else if (editingId && imagePreview) {
            vendorData.imageUrl = imagePreview;
        }

        try {
            if (editingId) {
                const vendorRef = doc(db, 'vendors', editingId);
                await updateDoc(vendorRef, vendorData);
            } else {
                await addDoc(collection(db, 'vendors'), vendorData);
            }
            resetForm();
        } catch (submitError) {
            console.error(submitError);
            alert('Failed to save vendor.');
        }
    };

    const iconContainerSx = {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        '& svg': {
            width: 20,
            height: 20,
        },
    } as const;

    return (
        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Paper variant="outlined" sx={{ p: { xs: 3, md: 4 }, borderRadius: 3 }}>
                <Stack
                    direction={{ xs: 'column', sm: 'row' }}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems={{ xs: 'flex-start', sm: 'center' }}
                >
                    <Stack direction="row" spacing={2} alignItems="center">
                        <Box
                            sx={{
                                ...iconContainerSx,
                                width: 48,
                                height: 48,
                                borderRadius: 2,
                                bgcolor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.35 : 0.12),
                                color: theme.palette.primary.main,
                            }}
                        >
                            <BuildingStorefrontIcon />
                        </Box>
                        <Typography variant="h4" fontWeight={700} color="text.primary">
                            Vendor Management
                        </Typography>
                    </Stack>
                    <Button
                        variant={isFormOpen ? 'outlined' : 'contained'}
                        color="primary"
                        startIcon={
                            <Box sx={{ ...iconContainerSx, color: 'inherit' }}>
                                <PlusIcon />
                            </Box>
                        }
                        onClick={() => setIsFormOpen((prev) => !prev)}
                        sx={{ textTransform: 'none' }}
                    >
                        {isFormOpen ? 'Close Form' : 'Add Vendor'}
                    </Button>
                </Stack>

                {isFormOpen && (
                    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 4 }}>
                        <Grid container spacing={2.5}>
                            <Grid item xs={12} md={6}>
                                <TextField label="Vendor Name" fullWidth value={name} onChange={(event) => setName(event.target.value)} required />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField
                                    label="Primary Contact"
                                    fullWidth
                                    value={contactPerson}
                                    onChange={(event) => setContactPerson(event.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField label="Phone" fullWidth value={phone} onChange={(event) => setPhone(event.target.value)} />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField label="Email" fullWidth value={email} onChange={(event) => setEmail(event.target.value)} />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    label="Address"
                                    fullWidth
                                    value={address}
                                    onChange={(event) => setAddress(event.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField
                                    label="Products Carried"
                                    fullWidth
                                    multiline
                                    minRows={3}
                                    helperText="Enter one product per line"
                                    value={productsCarried}
                                    onChange={(event) => setProductsCarried(event.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12} md={3}>
                                <TextField
                                    label="Lead Time (days)"
                                    fullWidth
                                    value={sourcingLeadTimeDays}
                                    onChange={(event) => setSourcingLeadTimeDays(event.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12} md={3}>
                                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems={{ xs: 'flex-start', sm: 'center' }}>
                                    <Avatar
                                        variant="rounded"
                                        src={imagePreview ?? undefined}
                                        alt={name}
                                        sx={{ width: 80, height: 80, borderRadius: 2 }}
                                    >
                                        {!imagePreview && <BuildingStorefrontIcon />}
                                    </Avatar>
                                    <Button
                                        variant="outlined"
                                        color="primary"
                                        startIcon={
                                            <Box sx={{ ...iconContainerSx, color: 'inherit' }}>
                                                <PhotoIcon />
                                            </Box>
                                        }
                                        component="label"
                                        sx={{ textTransform: 'none' }}
                                    >
                                        Upload Logo
                                        <input hidden accept="image/*" type="file" onChange={handleImageSelect} />
                                    </Button>
                                </Stack>
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    label="Notes"
                                    fullWidth
                                    multiline
                                    minRows={3}
                                    value={notes}
                                    onChange={(event) => setNotes(event.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <Stack direction="row" spacing={2} justifyContent="flex-end">
                                    <Button variant="outlined" color="inherit" onClick={resetForm} sx={{ textTransform: 'none' }}>
                                        Cancel
                                    </Button>
                                    <Button type="submit" variant="contained" color="primary" sx={{ textTransform: 'none' }}>
                                        {editingId ? 'Update Vendor' : 'Save Vendor'}
                                    </Button>
                                </Stack>
                            </Grid>
                        </Grid>
                    </Box>
                )}
            </Paper>

            {loading ? (
                <Box sx={{ display: 'grid', placeItems: 'center', py: 6 }}>
                    <CircularProgress size={24} />
                </Box>
            ) : error ? (
                <Alert severity="error">{error}</Alert>
            ) : (
                <Grid container spacing={3}>
                    {vendors.length === 0 ? (
                        <Grid item xs={12}>
                            <Paper variant="outlined" sx={{ p: 4, textAlign: 'center', borderRadius: 2 }}>
                                <Typography variant="body1" color="text.secondary">
                                    No vendors added yet.
                                </Typography>
                            </Paper>
                        </Grid>
                    ) : (
                        vendors.map((vendor) => (
                            <Grid item xs={12} md={6} key={vendor.id}>
                                <Paper
                                    variant="outlined"
                                    sx={{
                                        p: 3,
                                        borderRadius: 2,
                                        height: '100%',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: 2,
                                        bgcolor: alpha(theme.palette.background.paper, theme.palette.mode === 'dark' ? 0.2 : 0.04),
                                    }}
                                >
                                    <Stack direction="row" spacing={2.5} alignItems="flex-start">
                                        <Avatar
                                            variant="rounded"
                                            src={vendor.imageUrl ?? undefined}
                                            alt={vendor.name}
                                            sx={{ width: 64, height: 64, borderRadius: 2 }}
                                        >
                                            {!vendor.imageUrl && <BuildingStorefrontIcon />}
                                        </Avatar>
                                        <Box sx={{ flexGrow: 1 }}>
                                            <Typography variant="h6" fontWeight={700} color="text.primary">
                                                {vendor.name}
                                            </Typography>
                                            {vendor.contactPerson && (
                                                <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                                                    {vendor.contactPerson}
                                                    {vendor.phone ? ` — ${vendor.phone}` : ''}
                                                </Typography>
                                            )}
                                            {vendor.sourcingLeadTimeDays && (
                                                <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                                                    Lead time:{' '}
                                                    <Typography component="span" variant="caption" sx={{ fontWeight: 600 }}>
                                                        {vendor.sourcingLeadTimeDays} days
                                                    </Typography>
                                                </Typography>
                                            )}
                                        </Box>
                                    </Stack>
                                    <Box>
                                        <Typography variant="subtitle2" color="text.primary">
                                            Products
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            {vendor.productsCarried?.join(', ') || 'N/A'}
                                        </Typography>
                                    </Box>
                                    {vendor.notes && (
                                        <Typography variant="body2" color="text.secondary">
                                            {vendor.notes}
                                        </Typography>
                                    )}
                                    <Stack direction="row" justifyContent="flex-end">
                                        <Button
                                            size="small"
                                            variant="text"
                                            color="primary"
                                            onClick={() => handleEdit(vendor)}
                                            sx={{ textTransform: 'none', fontWeight: 600 }}
                                        >
                                            Edit
                                        </Button>
                                    </Stack>
                                </Paper>
                            </Grid>
                        ))
                    )}
                </Grid>
            )}
        </Box>
    );
};

VendorsView.displayName = 'VendorsView';
