import React, { useEffect, useState } from 'react';
import {
    Alert,
    Avatar,
    Box,
    Button,
    Chip,
    CircularProgress,
    Grid,
    Paper,
    Stack,
    TextField,
    Typography,
} from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import { collection, onSnapshot, query, orderBy, addDoc, updateDoc, doc, QuerySnapshot, DocumentData } from 'firebase/firestore';
import { db } from '../firebase/config';
import { Species } from '../types';
import { FishIcon, PlusIcon, PhotoIcon } from './IconComponents';
import { uploadImage } from '../utils/imageUtils';
import { docToPlainObject } from '../utils/firestoreUtils';

export const SpeciesView: React.FC = () => {
    const theme = useTheme();
    const [speciesList, setSpeciesList] = useState<Species[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isFormOpen, setIsFormOpen] = useState(false);

    const [editingId, setEditingId] = useState<string | null>(null);
    const [name, setName] = useState('');
    const [productForms, setProductForms] = useState('');
    const [qualityControlNotes, setQualityControlNotes] = useState('');
    const [imageFile, setImageFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(null);

    useEffect(() => {
        try {
            const listQuery = query(collection(db, 'species'), orderBy('name'));
            const unsubscribe = onSnapshot(
                listQuery,
                (snapshot: QuerySnapshot<DocumentData>) => {
                    const list = snapshot.docs.map((docSnapshot) => docToPlainObject<Species>(docSnapshot));
                    setSpeciesList(list);
                    setLoading(false);
                },
                (snapshotError) => {
                    console.error(snapshotError);
                    setError('Failed to fetch species data.');
                    setLoading(false);
                },
            );
            return () => unsubscribe();
        } catch (initializationError) {
            console.error(initializationError);
            setError('Failed to initialize Firebase for species.');
            setLoading(false);
        }
    }, []);

    const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            const file = event.target.files[0];
            setImageFile(file);
            setImagePreview(URL.createObjectURL(file));
        }
    };

    const resetForm = () => {
        setEditingId(null);
        setName('');
        setProductForms('');
        setQualityControlNotes('');
        setImageFile(null);
        setImagePreview(null);
        setIsFormOpen(false);
    };

    const handleEdit = (species: Species) => {
        setEditingId(species.id);
        setName(species.name);
        setProductForms(species.productForms.join('\n'));
        setQualityControlNotes(species.qualityControlNotes);
        setImagePreview(species.imageUrl || null);
        setImageFile(null);
        setIsFormOpen(true);
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        if (!name.trim() || !qualityControlNotes.trim()) {
            alert('Species name and quality control notes are required.');
            return;
        }

        const formsArray = productForms
            .split('\n')
            .map((form) => form.trim())
            .filter(Boolean);

        const speciesData: Partial<Species> = {
            name: name.trim(),
            productForms: formsArray,
            qualityControlNotes: qualityControlNotes.trim(),
        };

        if (imageFile) {
            try {
                const imageUrl = await uploadImage(imageFile, `species/${Date.now()}_${imageFile.name}`);
                speciesData.imageUrl = imageUrl;
            } catch (uploadError) {
                console.error(uploadError);
                alert('Failed to upload image.');
                return;
            }
        } else if (editingId && imagePreview) {
            speciesData.imageUrl = imagePreview;
        }

        try {
            if (editingId) {
                const speciesRef = doc(db, 'species', editingId);
                await updateDoc(speciesRef, speciesData);
            } else {
                await addDoc(collection(db, 'species'), speciesData);
            }
            resetForm();
        } catch (submitError) {
            console.error('Error saving species: ', submitError);
            alert('Failed to save species data.');
        }
    };

    const iconContainerSx = {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        '& svg': {
            width: 20,
            height: 20,
        },
    } as const;

    return (
        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Paper variant="outlined" sx={{ p: { xs: 3, md: 4 }, borderRadius: 3 }}>
                <Stack
                    direction={{ xs: 'column', sm: 'row' }}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems={{ xs: 'flex-start', sm: 'center' }}
                >
                    <Stack direction="row" spacing={2} alignItems="center">
                        <Box
                            sx={{
                                ...iconContainerSx,
                                width: 48,
                                height: 48,
                                borderRadius: 2,
                                bgcolor: alpha(theme.palette.success.main, theme.palette.mode === 'dark' ? 0.35 : 0.12),
                                color: theme.palette.success.main,
                            }}
                        >
                            <FishIcon />
                        </Box>
                        <Typography variant="h4" fontWeight={700} color="text.primary">
                            Species Information
                        </Typography>
                    </Stack>
                    <Button
                        variant={isFormOpen ? 'outlined' : 'contained'}
                        color="success"
                        startIcon={
                            <Box sx={{ ...iconContainerSx, color: 'inherit' }}>
                                <PlusIcon />
                            </Box>
                        }
                        onClick={() => setIsFormOpen((prev) => !prev)}
                        sx={{ textTransform: 'none' }}
                    >
                        {isFormOpen ? 'Close Form' : 'Add Species'}
                    </Button>
                </Stack>

                {isFormOpen && (
                    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 4 }}>
                        <Grid container spacing={2.5}>
                            <Grid item xs={12} md={6}>
                                <TextField label="Species Name" fullWidth required value={name} onChange={(event) => setName(event.target.value)} />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems={{ xs: 'flex-start', sm: 'center' }}>
                                    <Avatar
                                        variant="rounded"
                                        src={imagePreview ?? undefined}
                                        alt={name}
                                        sx={{ width: 80, height: 80, borderRadius: 2 }}
                                    >
                                        {!imagePreview && <FishIcon />}
                                    </Avatar>
                                    <Button
                                        variant="outlined"
                                        color="success"
                                        startIcon={
                                            <Box sx={{ ...iconContainerSx, color: 'inherit' }}>
                                                <PhotoIcon />
                                            </Box>
                                        }
                                        component="label"
                                        sx={{ textTransform: 'none' }}
                                    >
                                        Upload Image
                                        <input hidden accept="image/*" type="file" onChange={handleImageSelect} />
                                    </Button>
                                </Stack>
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    label="Product Forms"
                                    fullWidth
                                    multiline
                                    minRows={3}
                                    helperText="Enter one form per line"
                                    value={productForms}
                                    onChange={(event) => setProductForms(event.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <TextField
                                    label="Quality Control Notes"
                                    fullWidth
                                    required
                                    multiline
                                    minRows={4}
                                    value={qualityControlNotes}
                                    onChange={(event) => setQualityControlNotes(event.target.value)}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <Stack direction="row" spacing={2} justifyContent="flex-end">
                                    <Button variant="outlined" color="inherit" onClick={resetForm} sx={{ textTransform: 'none' }}>
                                        Cancel
                                    </Button>
                                    <Button type="submit" variant="contained" color="success" sx={{ textTransform: 'none' }}>
                                        {editingId ? 'Update Species' : 'Save Species'}
                                    </Button>
                                </Stack>
                            </Grid>
                        </Grid>
                    </Box>
                )}
            </Paper>

            {loading ? (
                <Box sx={{ display: 'grid', placeItems: 'center', py: 6 }}>
                    <CircularProgress size={24} color="success" />
                </Box>
            ) : error ? (
                <Alert severity="error">{error}</Alert>
            ) : (
                <Grid container spacing={3}>
                    {speciesList.length === 0 ? (
                        <Grid item xs={12}>
                            <Paper variant="outlined" sx={{ p: 4, textAlign: 'center', borderRadius: 2 }}>
                                <Typography variant="body1" color="text.secondary">
                                    No species added yet.
                                </Typography>
                            </Paper>
                        </Grid>
                    ) : (
                        speciesList.map((species) => (
                            <Grid item xs={12} md={6} key={species.id}>
                                <Paper
                                    variant="outlined"
                                    sx={{
                                        p: 3,
                                        borderRadius: 2,
                                        height: '100%',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        gap: 2,
                                        bgcolor: alpha(theme.palette.background.paper, theme.palette.mode === 'dark' ? 0.2 : 0.04),
                                    }}
                                >
                                    <Stack direction="row" spacing={2.5} alignItems="flex-start">
                                        <Avatar
                                            variant="rounded"
                                            src={species.imageUrl ?? undefined}
                                            alt={species.name}
                                            sx={{ width: 64, height: 64, borderRadius: 2 }}
                                        >
                                            {!species.imageUrl && <FishIcon />}
                                        </Avatar>
                                        <Box sx={{ flexGrow: 1 }}>
                                            <Typography variant="h6" fontWeight={700} color="text.primary">
                                                {species.name}
                                            </Typography>
                                            <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mt: 1 }}>
                                                {species.productForms.map((form) => (
                                                    <Chip key={form} label={form} color="success" size="small" variant="outlined" />
                                                ))}
                                            </Stack>
                                        </Box>
                                    </Stack>
                                    <Typography variant="body2" color="text.secondary">
                                        {species.qualityControlNotes}
                                    </Typography>
                                    <Stack direction="row" justifyContent="flex-end">
                                        <Button
                                            size="small"
                                            variant="text"
                                            color="success"
                                            onClick={() => handleEdit(species)}
                                            sx={{ textTransform: 'none', fontWeight: 600 }}
                                        >
                                            Edit
                                        </Button>
                                    </Stack>
                                </Paper>
                            </Grid>
                        ))
                    )}
                </Grid>
            )}
        </Box>
    );
};

SpeciesView.displayName = 'SpeciesView';
