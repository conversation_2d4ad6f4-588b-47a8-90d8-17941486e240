import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FilterListIcon from '@mui/icons-material/FilterList';
import {
  alpha,
  useTheme,
} from '@mui/material/styles';
import {
  Avatar,
  Box,
  Button,
  Chip,
  Checkbox,
  Collapse,
  Divider,
  FormControl,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  OutlinedInput,
  Paper,
  Select,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
  useMediaQuery,
} from '@mui/material';
import Grid from '@mui/material/Grid';
import React, { useMemo, useState } from 'react';
import {
  EmailContext,
  HACCPEvent,
  Species,
  Vendor,
  Location,
  EventType,
} from '../types';
import {
  PackageIcon,
  ShoppingCartIcon,
  TrashIcon,
  ClipboardIcon,
  ChartBarIcon,
  PencilSquareIcon,
  SparklesIcon,
  ThermometerIcon,
  ArchiveBoxIcon,
  UserCircleIcon,
  ClipboardDocumentListIcon,
  ChartPieIcon,
  TruckIcon,
} from './IconComponents';
import { InventoryDisplay } from './InventoryDisplay';

interface DashboardProps {
  events: HACCPEvent[];
  species: Species[];
  vendors: Vendor[];
  locations: Location[];
  onEditEvent: (event: HACCPEvent) => void;
  onDraftEmail: (context: EmailContext) => void;
  onUpdateEventField: (eventId: string, field: keyof HACCPEvent, value: unknown) => Promise<boolean>;
}

type PaletteKey = 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';

const eventTypeConfig: Record<EventType, { icon: React.ReactNode; label: string; palette: PaletteKey }> = {
  receiving: { icon: <PackageIcon />, label: 'Receiving', palette: 'primary' },
  sales: { icon: <ShoppingCartIcon />, label: 'Sales', palette: 'success' },
  disposal: { icon: <TrashIcon />, label: 'Disposal', palette: 'error' },
  're-sealing': { icon: <ClipboardIcon />, label: 'Re-sealing', palette: 'secondary' },
  relocation: { icon: <TruckIcon />, label: 'Relocation', palette: 'info' },
  sanitation: { icon: <SparklesIcon />, label: 'Sanitation', palette: 'success' },
  'thermometer-calibration': { icon: <ThermometerIcon />, label: 'Calibration', palette: 'warning' },
  inventory: { icon: <ArchiveBoxIcon />, label: 'Inventory', palette: 'warning' },
  'employee-training': { icon: <UserCircleIcon />, label: 'Training', palette: 'info' },
};

const EventTypeChip: React.FC<{ eventType: EventType }> = ({ eventType }) => {
  const theme = useTheme();
  const fallback: { icon: React.ReactNode; label: string; palette: PaletteKey } = {
    icon: <ChartPieIcon />,
    label: eventType,
    palette: 'primary',
  };
  const config = eventTypeConfig[eventType] ?? fallback;
  const paletteColor = theme.palette[config.palette];
  const backgroundColor = alpha(paletteColor.main, 0.12);
  const textColor =
    theme.palette.mode === 'light'
      ? paletteColor.dark ?? paletteColor.main
      : paletteColor.light ?? paletteColor.main;

  return (
    <Chip
      size="small"
      icon={
        config.icon ? (
          <Box sx={{ display: 'inline-flex', color: paletteColor.main, '& svg': { width: 16, height: 16 } }}>
            {config.icon}
          </Box>
        ) : undefined
      }
      label={config.label}
      sx={{
        bgcolor: backgroundColor,
        color: textColor,
        fontWeight: 600,
        '& .MuiChip-icon': { color: paletteColor.main, ml: 0.25 },
        textTransform: 'capitalize',
      }}
    />
  );
};

const IconWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Box sx={{ display: 'inline-flex', '& svg': { width: 24, height: 24 } }}>{children}</Box>
);

const KpiCard: React.FC<{ title: string; value: string; icon: React.ReactNode; palette?: PaletteKey }> = ({
  title,
  value,
  icon,
  palette = 'primary',
}) => {
  const theme = useTheme();
  const paletteColor = theme.palette[palette];

  return (
    <Paper
      variant="outlined"
      sx={{
        p: 3,
        borderRadius: 3,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        height: '100%',
      }}
    >
      <Avatar
        variant="rounded"
        sx={{
          bgcolor: alpha(paletteColor.main, 0.15),
          color: paletteColor.main,
          width: 52,
          height: 52,
        }}
      >
        <IconWrapper>{icon}</IconWrapper>
      </Avatar>
      <Box>
        <Typography variant="body2" color="text.secondary">
          {title}
        </Typography>
        <Typography variant="h5" fontWeight={700}>
          {value}
        </Typography>
      </Box>
    </Paper>
  );
};

const EventPieChart: React.FC<{ data: { type: EventType; count: number; percentage: number; color: string }[] }> = ({
  data,
}) => {
  const theme = useTheme();
  const radius = 60;
  const circumference = 2 * Math.PI * radius;
  let accumulatedOffset = 0;

  return (
    <Paper
      variant="outlined"
      sx={{
        p: 3,
        borderRadius: 3,
        display: 'flex',
        flexDirection: { xs: 'column', md: 'row' },
        alignItems: 'center',
        gap: 3,
        height: '100%',
      }}
    >
      <Box sx={{ width: 160, height: 160 }}>
        <Box
          component="svg"
          viewBox="0 0 140 140"
          sx={{ width: '100%', height: '100%', transform: 'rotate(-90deg)' }}
        >
          {data.map(({ percentage, color }, index) => {
            const strokeDashoffset = circumference - (percentage / 100) * circumference;
            const rotation = (accumulatedOffset / 100) * 360;
            accumulatedOffset += percentage;
            return (
              <circle
                key={index}
                r={radius}
                cx="70"
                cy="70"
                fill="transparent"
                stroke={color}
                strokeWidth="20"
                strokeDasharray={circumference}
                strokeDashoffset={strokeDashoffset}
                style={{ transform: `rotate(${rotation}deg)`, transformOrigin: '70px 70px' }}
                strokeLinecap="round"
              />
            );
          })}
        </Box>
      </Box>
      <Box sx={{ flex: 1, width: '100%' }}>
        <Typography variant="subtitle1" fontWeight={600} mb={1}>
          Event Distribution
        </Typography>
        <Stack spacing={1.5}>
          {data.map(({ type, count, percentage, color }) => (
            <Stack key={type} direction="row" justifyContent="space-between" alignItems="center" spacing={2}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Box
                  sx={{
                    width: 10,
                    height: 10,
                    borderRadius: '50%',
                    bgcolor: color,
                  }}
                />
                <Typography variant="body2" color="text.secondary" sx={{ textTransform: 'capitalize' }}>
                  {eventTypeConfig[type]?.label || type}
                </Typography>
              </Stack>
              <Typography variant="body2" fontWeight={600} color="text.primary">
                {percentage.toFixed(1)}% ({count})
              </Typography>
            </Stack>
          ))}
          {data.length === 0 && (
            <Typography variant="body2" color="text.secondary">
              No events recorded for the selected period.
            </Typography>
          )}
        </Stack>
      </Box>
    </Paper>
  );
};

const EventBarChart: React.FC<{ data: { date: string; count: number }[] }> = ({ data }) => {
  const theme = useTheme();
  const maxCount = Math.max(...data.map((d) => d.count), 1);
  const labels = data.map((d) =>
    new Date(`${d.date}T00:00:00`).toLocaleDateString('en-US', { weekday: 'short' }),
  );

  return (
    <Paper
      variant="outlined"
      sx={{
        p: 3,
        borderRadius: 3,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
      }}
    >
      <Typography variant="subtitle1" fontWeight={600}>
        Activity Last 7 Days
      </Typography>
      <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 2, height: 180 }}>
        {data.map((item, index) => (
          <Stack key={item.date} alignItems="center" spacing={0.5} sx={{ flex: 1 }}>
            <Box
              sx={{
                width: '100%',
                height: `${(item.count / maxCount) * 100}%`,
                bgcolor: alpha(theme.palette.primary.main, 0.45),
                borderRadius: 1,
                minHeight: 8,
              }}
              title={`${item.count} events on ${labels[index]}`}
            />
            <Typography variant="caption" color="text.secondary">
              {labels[index]}
            </Typography>
          </Stack>
        ))}
      </Box>
    </Paper>
  );
};

const multiSelectMenuProps = {
  PaperProps: {
    style: {
      maxHeight: 260,
    },
  },
};

const initialFilters = {
  startDate: '',
  endDate: '',
  eventTypes: [] as string[],
  species: [] as string[],
  vendors: [] as string[],
  locations: [] as string[],
  batchNumber: '',
};

export const Dashboard: React.FC<DashboardProps> = ({
  events,
  species,
  vendors,
  locations,
  onEditEvent,
  onDraftEmail,
  onUpdateEventField,
}) => {
  const theme = useTheme();
  const isMdDown = useMediaQuery(theme.breakpoints.down('md'));
  const [filters, setFilters] = useState(initialFilters);
  const [showFilters, setShowFilters] = useState(false);

  const handleFilterChange = (filterName: keyof typeof initialFilters, value: unknown) => {
    setFilters((prev) => ({ ...prev, [filterName]: value }));
  };

  const handleSelectArrayChange = (
    filterName: 'eventTypes' | 'species' | 'vendors' | 'locations',
    value: unknown,
  ) => {
    const nextValue = typeof value === 'string' ? value.split(',') : (value as string[]);
    handleFilterChange(filterName, nextValue);
  };

  const filteredEvents = useMemo(() => {
    return events.filter((event) => {
      if (filters.startDate && event.date < filters.startDate) return false;
      if (filters.endDate && event.date > filters.endDate) return false;
      if (filters.eventTypes.length > 0 && !filters.eventTypes.includes(event.eventType)) return false;
      if (filters.species.length > 0 && (!event.product || !filters.species.includes(event.product))) return false;
      if (filters.vendors.length > 0 && (!event.supplier || !filters.vendors.includes(event.supplier))) return false;
      if (filters.locations.length > 0 && (!event.location || !filters.locations.includes(event.location))) return false;
      if (
        filters.batchNumber &&
        (!event.batchNumber || !event.batchNumber.toLowerCase().includes(filters.batchNumber.toLowerCase()))
      ) {
        return false;
      }
      return true;
    });
  }, [events, filters]);

  const eventTypes = useMemo(() => [...new Set(events.map((e) => e.eventType))], [events]);
  const productRequiredEventTypes: EventType[] = useMemo(
    () => ['receiving', 'sales', 'disposal', 're-sealing', 'inventory', 'relocation'],
    [],
  );

  const kpiData = useMemo(() => {
    const today = new Date();
    const oneWeekAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 7);
    const oneMonthAgo = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 30);

    const eventsLastWeek = events.filter((e) => new Date(e.date) >= oneWeekAgo);
    const receivingLastMonth = events.filter(
      (e) => e.eventType === 'receiving' && new Date(e.date) >= oneMonthAgo,
    );

    const eventTypeCounts = events.reduce((acc, event) => {
      acc[event.eventType] = (acc[event.eventType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostFrequent = Object.entries(eventTypeCounts).sort(
      (a, b) => (b[1] as number) - (a[1] as number),
    )[0];

    return {
      totalEventsLastWeek: eventsLastWeek.length,
      totalWeightReceived: receivingLastMonth.reduce((sum, e) => sum + (e.quantity || 0), 0),
      mostFrequentEventType: mostFrequent ? eventTypeConfig[mostFrequent[0] as EventType]?.label : 'N/A',
    };
  }, [events]);

  const chartData = useMemo(() => {
    const eventTypeCounts = events.reduce((acc, event) => {
      acc[event.eventType] = (acc[event.eventType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalEvents = events.length;
    const pieChartData = Object.entries(eventTypeCounts)
      .map(([type, countValue]) => {
        const count = countValue as number;
        return {
          type: type as EventType,
          count,
          percentage: totalEvents > 0 ? (count / totalEvents) * 100 : 0,
          color: eventTypeConfig[type as EventType]?.palette
            ? theme.palette[eventTypeConfig[type as EventType].palette].main
            : theme.palette.grey[500],
        };
      })
      .sort((a, b) => b.count - a.count);

    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const d = new Date();
      d.setDate(d.getDate() - i);
      return d.toISOString().split('T')[0];
    }).reverse();

    const barChartData = last7Days.map((date) => {
      const count = events.filter((e) => e.date === date).length;
      return { date, count };
    });

    return { pieChartData, barChartData };
  }, [events, theme]);

  const renderFilters = () => (
    <Paper
      variant="outlined"
      sx={{
        p: 3,
        borderRadius: 3,
        bgcolor: alpha(theme.palette.background.paper, theme.palette.mode === 'light' ? 1 : 0.6),
      }}
    >
      <Button
        onClick={() => setShowFilters((prev) => !prev)}
        fullWidth
        variant="outlined"
        startIcon={<FilterListIcon />}
        endIcon={showFilters ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        sx={{ justifyContent: 'space-between', fontWeight: 600 }}
      >
        Filter Events
      </Button>
      <Collapse in={showFilters} timeout="auto" unmountOnExit>
        <Grid container spacing={2.5} mt={1}>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              type="date"
              label="Start Date"
              value={filters.startDate}
              onChange={(e) => handleFilterChange('startDate', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              type="date"
              label="End Date"
              value={filters.endDate}
              onChange={(e) => handleFilterChange('endDate', e.target.value)}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel id="event-types-label">Event Types</InputLabel>
              <Select
                labelId="event-types-label"
                multiple
                value={filters.eventTypes}
                onChange={(event) => handleSelectArrayChange('eventTypes', event.target.value)}
                input={<OutlinedInput label="Event Types" />}
                renderValue={(selected) => (selected as string[]).join(', ')}
                MenuProps={multiSelectMenuProps}
              >
                {eventTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    <Checkbox checked={filters.eventTypes.indexOf(type) > -1} />
                    <ListItemText primary={eventTypeConfig[type]?.label || type} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel id="species-label">Species</InputLabel>
              <Select
                labelId="species-label"
                multiple
                value={filters.species}
                onChange={(event) => handleSelectArrayChange('species', event.target.value)}
                input={<OutlinedInput label="Species" />}
                renderValue={(selected) => (selected as string[]).join(', ')}
                MenuProps={multiSelectMenuProps}
              >
                {species.map((s) => (
                  <MenuItem key={s.id} value={s.name}>
                    <Checkbox checked={filters.species.indexOf(s.name) > -1} />
                    <ListItemText primary={s.name} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel id="vendors-label">Vendors</InputLabel>
              <Select
                labelId="vendors-label"
                multiple
                value={filters.vendors}
                onChange={(event) => handleSelectArrayChange('vendors', event.target.value)}
                input={<OutlinedInput label="Vendors" />}
                renderValue={(selected) => (selected as string[]).join(', ')}
                MenuProps={multiSelectMenuProps}
              >
                {vendors.map((v) => (
                  <MenuItem key={v.id} value={v.name}>
                    <Checkbox checked={filters.vendors.indexOf(v.name) > -1} />
                    <ListItemText primary={v.name} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth>
              <InputLabel id="locations-label">Locations</InputLabel>
              <Select
                labelId="locations-label"
                multiple
                value={filters.locations}
                onChange={(event) => handleSelectArrayChange('locations', event.target.value)}
                input={<OutlinedInput label="Locations" />}
                renderValue={(selected) => (selected as string[]).join(', ')}
                MenuProps={multiSelectMenuProps}
              >
                {locations.map((l) => (
                  <MenuItem key={l.id} value={l.name}>
                    <Checkbox checked={filters.locations.indexOf(l.name) > -1} />
                    <ListItemText primary={l.name} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              label="Batch Number"
              value={filters.batchNumber}
              onChange={(e) => handleFilterChange('batchNumber', e.target.value)}
              placeholder="Search batch..."
            />
          </Grid>
          <Grid item xs={12} md={3} display="flex" alignItems="flex-end">
            <Button
              fullWidth
              variant="contained"
              color="inherit"
              onClick={() => setFilters({ ...initialFilters })}
            >
              Reset Filters
            </Button>
          </Grid>
        </Grid>
      </Collapse>
    </Paper>
  );

  const renderMobileCards = () => (
    <Stack spacing={2}>
      {filteredEvents.map((event) => {
        const isProductMissing = productRequiredEventTypes.includes(event.eventType) && !event.product;
        return (
          <Paper key={event.id} variant="outlined" sx={{ p: 2.5, borderRadius: 3 }}>
            <Stack direction="row" justifyContent="space-between" alignItems="flex-start" mb={1.5}>
              <EventTypeChip eventType={event.eventType} />
              <Typography variant="caption" color="text.secondary" textAlign="right">
                {event.date}
                <br />
                {event.time}
              </Typography>
            </Stack>
            <Typography variant="subtitle1" fontWeight={600} color="text.primary">
              {isProductMissing ? (
                <TextField
                  select
                  fullWidth
                  size="small"
                  value=""
                  onChange={(e) => {
                    if (e.target.value) {
                      void onUpdateEventField(event.id, 'product', e.target.value);
                    }
                  }}
                  SelectProps={{ displayEmpty: true }}
                >
                  <MenuItem value="" disabled>
                    Select product
                  </MenuItem>
                  {species.map((s) => (
                    <MenuItem key={s.id} value={s.name}>
                      {s.name}
                    </MenuItem>
                  ))}
                </TextField>
              ) : event.product ? (
                [event.product, event.productForm].filter(Boolean).join(' - ')
              ) : (
                event.employeeName || 'N/A'
              )}
            </Typography>
            <Grid container spacing={1} sx={{ mt: 1.5 }}>
              {event.quantity && (
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary">
                    Qty
                  </Typography>
                  <Typography variant="body2">{`${event.quantity.toFixed(2)} ${event.unit}`}</Typography>
                </Grid>
              )}
              {event.batchNumber && (
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary">
                    Batch
                  </Typography>
                  <Typography variant="body2" fontFamily="monospace">
                    {event.batchNumber}
                  </Typography>
                </Grid>
              )}
              {(event.supplier || event.fromLocation) && (
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary">
                    From
                  </Typography>
                  <Typography variant="body2">{event.supplier || event.fromLocation}</Typography>
                </Grid>
              )}
              {event.location && (
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary">
                    To
                  </Typography>
                  <Typography variant="body2">{event.location}</Typography>
                </Grid>
              )}
              {event.createdBy && (
                <Grid item xs={6}>
                  <Typography variant="caption" color="text.secondary">
                    User
                  </Typography>
                  <Typography variant="body2">{event.createdBy}</Typography>
                </Grid>
              )}
            </Grid>
            <Divider sx={{ my: 2 }} />
            <Stack direction="row" justifyContent="flex-end" spacing={1.5}>
              {event.eventType === 'receiving' && (
                <Button
                  size="small"
                  color="secondary"
                  onClick={() => onDraftEmail({ type: 'receiving', event })}
                >
                  Email
                </Button>
              )}
              <Button
                size="small"
                startIcon={
                  <Box sx={{ display: 'inline-flex', '& svg': { width: 16, height: 16 } }}>
                    <PencilSquareIcon />
                  </Box>
                }
                onClick={() => onEditEvent(event)}
              >
                Edit
              </Button>
            </Stack>
          </Paper>
        );
      })}
    </Stack>
  );

  const renderTable = () => (
    <TableContainer component={Paper} variant="outlined" sx={{ borderRadius: 3 }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Date</TableCell>
            <TableCell>Type</TableCell>
            <TableCell>Details</TableCell>
            <TableCell>Qty</TableCell>
            <TableCell>Batch #</TableCell>
            <TableCell>Vendor / From</TableCell>
            <TableCell>Location / To</TableCell>
            <TableCell>User</TableCell>
            <TableCell align="right">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {filteredEvents.map((event) => {
            const isProductMissing = productRequiredEventTypes.includes(event.eventType) && !event.product;
            return (
              <TableRow key={event.id} hover>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" color="text.secondary">
                    {event.date} {event.time}
                  </Typography>
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                  <EventTypeChip eventType={event.eventType} />
                </TableCell>
                <TableCell sx={{ minWidth: 180 }}>
                  {isProductMissing ? (
                    <TextField
                      select
                      fullWidth
                      size="small"
                      value=""
                      onChange={(e) => {
                        if (e.target.value) {
                          void onUpdateEventField(event.id, 'product', e.target.value);
                        }
                      }}
                      SelectProps={{ displayEmpty: true }}
                      aria-label={`Select product for event on ${event.date}`}
                    >
                      <MenuItem value="" disabled>
                        Select product
                      </MenuItem>
                      {species.map((s) => (
                        <MenuItem key={s.id} value={s.name}>
                          {s.name}
                        </MenuItem>
                      ))}
                    </TextField>
                  ) : (
                    <Typography variant="body2" fontWeight={600}>
                      {event.product
                        ? [event.product, event.productForm].filter(Boolean).join(' - ')
                        : event.employeeName || 'N/A'}
                    </Typography>
                  )}
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" color="text.secondary">
                    {event.quantity ? `${event.quantity.toFixed(2)} ${event.unit}` : 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" color="text.secondary" fontFamily="monospace">
                    {event.batchNumber || 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" color="text.secondary">
                    {event.supplier || event.fromLocation || 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" color="text.secondary">
                    {event.location || 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>
                  <Typography variant="body2" color="text.secondary">
                    {event.createdBy || '—'}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Stack direction="row" spacing={1.5} justifyContent="flex-end">
                    <IconButton size="small" onClick={() => onEditEvent(event)} aria-label="Edit event">
                      <Box sx={{ display: 'inline-flex', '& svg': { width: 18, height: 18 } }}>
                        <PencilSquareIcon />
                      </Box>
                    </IconButton>
                    {event.eventType === 'receiving' && (
                      <Button
                        size="small"
                        color="secondary"
                        onClick={() => onDraftEmail({ type: 'receiving', event })}
                      >
                        Email
                      </Button>
                    )}
                  </Stack>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );

  return (
    <Paper
      variant="outlined"
      sx={{
        borderRadius: 4,
        p: { xs: 3, md: 4 },
        maxWidth: 1200,
        width: '100%',
        mx: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: 4,
      }}
    >
      <InventoryDisplay events={events} />

      <Grid container spacing={2.5}>
        <Grid item xs={12} md={4}>
          <KpiCard
            title="Events This Week"
            value={kpiData.totalEventsLastWeek.toString()}
            icon={<ClipboardDocumentListIcon />}
            palette="secondary"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <KpiCard
            title="Receiving This Month"
            value={`${kpiData.totalWeightReceived.toFixed(1)} lbs`}
            icon={<PackageIcon />}
            palette="primary"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <KpiCard title="Top Activity" value={kpiData.mostFrequentEventType || 'N/A'} icon={<SparklesIcon />} palette="warning" />
        </Grid>
      </Grid>

      <Grid container spacing={2.5}>
        <Grid item xs={12} lg={6}>
          <EventPieChart data={chartData.pieChartData} />
        </Grid>
        <Grid item xs={12} lg={6}>
          <EventBarChart data={chartData.barChartData} />
        </Grid>
      </Grid>

      <Box>
        <Stack direction="row" alignItems="center" spacing={2} mb={3}>
          <Avatar
            variant="rounded"
            sx={{
              bgcolor: alpha(theme.palette.primary.main, 0.15),
              color: theme.palette.primary.main,
              width: 48,
              height: 48,
            }}
          >
            <IconWrapper>
              <ChartBarIcon />
            </IconWrapper>
          </Avatar>
          <Typography variant="h5" fontWeight={700}>
            Detailed Event History
          </Typography>
        </Stack>

        {renderFilters()}

        {filteredEvents.length === 0 ? (
          <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 6 }}>
            No events match the current filters.
          </Typography>
        ) : isMdDown ? (
          renderMobileCards()
        ) : (
          renderTable()
        )}
      </Box>
    </Paper>
  );
};
