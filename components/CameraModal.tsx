import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { GoogleGenAI, Type } from '@google/genai';
import {
    <PERSON>ert,
    Box,
    Button,
    Chip,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    InputAdornment,
    Paper,
    Stack,
    TextField,
    Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { alpha, useTheme } from '@mui/material/styles';
import { HACCPEvent, Species, Vendor, Location } from '../types';
import { CameraIcon, TrashIcon } from './IconComponents';

interface CameraModalProps {
    species: Species[];
    vendors: Vendor[];
    locations: Location[];
    onClose: () => void;
    onSave: (events: Partial<HACCPEvent>[]) => void;
}

type ModalStep = 'camera' | 'preview' | 'loading' | 'confirm';

export const CameraModal: React.FC<CameraModalProps> = ({ species, vendors, locations, onClose, onSave }) => {
    const theme = useTheme();
    const [step, setStep] = useState<ModalStep>('camera');
    const [capturedImage, setCapturedImage] = useState<string | null>(null);
    const [extractedEvents, setExtractedEvents] = useState<Partial<HACCPEvent>[]>([]);
    const [error, setError] = useState<string | null>(null);
    const videoRef = useRef<HTMLVideoElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const streamRef = useRef<MediaStream | null>(null);

    const stopCamera = useCallback(() => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach((track) => track.stop());
            streamRef.current = null;
        }
    }, []);

    useEffect(() => {
        const startCamera = async () => {
            if (step === 'camera' && !streamRef.current) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } });
                    streamRef.current = stream;
                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                    }
                } catch (cameraError) {
                    console.error('Error accessing camera:', cameraError);
                    setError('Could not access camera. Please check permissions.');
                }
            }
        };

        startCamera();

        return () => {
            stopCamera();
        };
    }, [step, stopCamera]);

    const handleTakePicture = () => {
        if (videoRef.current && canvasRef.current) {
            const video = videoRef.current;
            const canvas = canvasRef.current;
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const context = canvas.getContext('2d');
            if (context) {
                context.drawImage(video, 0, 0, canvas.width, canvas.height);
                const dataUrl = canvas.toDataURL('image/jpeg');
                setCapturedImage(dataUrl);
                setStep('preview');
                stopCamera();
            }
        }
    };

    const handleUsePicture = async () => {
        if (!capturedImage) return;
        setStep('loading');
        setError(null);

        const geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY;
        if (!geminiApiKey) {
            setError('Gemini API key is not configured. Please add VITE_GEMINI_API_KEY to your environment settings.');
            setStep('preview');
            return;
        }

        try {
            const ai = new GoogleGenAI({ apiKey: geminiApiKey });
            const base64Data = capturedImage.split(',')[1];
            const imagePart = { inlineData: { mimeType: 'image/jpeg', data: base64Data } };

            const response = await ai.models.generateContent({
                model: 'gemini-2.5-flash',
                contents: { parts: [imagePart] },
                config: {
                    systemInstruction:
                        "You are an intelligent data entry assistant for a seafood company. Analyze this image of a document (e.g., an invoice, receiving log, or inventory sheet). Extract all distinct line items as separate events. Structure your response as a JSON array of objects, where each object represents one event. Use the provided schema. Extract all available columns of data, such as product name, quantity, supplier, batch number, location, temperature, product form, and unit. Map them to the fields in the provided JSON schema. Infer the 'eventType' from the document's title or content (e.g., 'receiving' for invoices, 'inventory' for stock takes). If a date is present, use it in YYYY-MM-DD format; otherwise, omit the date field. For quantities, provide a number. Be precise. If a value for a field isn't in the document, omit that key from the object. Your output MUST be ONLY the JSON array.",
                    responseMimeType: 'application/json',
                    responseSchema: {
                        type: Type.ARRAY,
                        items: {
                            type: Type.OBJECT,
                            properties: {
                                eventType: {
                                    type: Type.STRING,
                                    enum: ['receiving', 'inventory', 'disposal', 'sales', 'sanitation', 'relocation', 're-sealing'],
                                },
                                product: { type: Type.STRING },
                                productForm: { type: Type.STRING },
                                quantity: { type: Type.NUMBER },
                                unit: { type: Type.STRING },
                                supplier: { type: Type.STRING },
                                location: { type: Type.STRING },
                                date: { type: Type.STRING },
                                batchNumber: { type: Type.STRING },
                                temperature: { type: Type.NUMBER },
                                notes: { type: Type.STRING },
                            },
                        },
                    },
                },
            });

            const potentialText = (response as any).text ?? (response as any).response?.text;
            const rawResponse =
                typeof potentialText === 'function' ? await potentialText.call((response as any).response ?? response) : potentialText;

            if (!rawResponse || typeof rawResponse !== 'string') {
                throw new Error('Gemini returned an empty response.');
            }

            const parsedEvents: Partial<HACCPEvent>[] = JSON.parse(rawResponse);
            setExtractedEvents(parsedEvents);
            setStep('confirm');
        } catch (aiError) {
            console.error('Error analyzing image:', aiError);
            setError('AI analysis failed. Please try a clearer picture.');
            setStep('preview');
        }
    };

    const handleDataChange = (index: number, field: keyof HACCPEvent, value: string | number) => {
        setExtractedEvents((prev) => prev.map((event, idx) => (idx === index ? { ...event, [field]: value } : event)));
    };

    const removeEvent = (index: number) => {
        setExtractedEvents((prev) => prev.filter((_, idx) => idx !== index));
    };

    const { newSpecies, newVendors, newLocations } = useMemo(() => {
        const existingSpecies = new Set(species.map((entry) => entry.name.toLowerCase()));
        const existingVendors = new Set(vendors.map((entry) => entry.name.toLowerCase()));
        const existingLocations = new Set(locations.map((entry) => entry.name.toLowerCase()));

        const speciesAccumulator = new Set<string>();
        const vendorAccumulator = new Set<string>();
        const locationAccumulator = new Set<string>();

        extractedEvents.forEach((event) => {
            if (event.product && !existingSpecies.has(event.product.toLowerCase())) {
                speciesAccumulator.add(event.product);
            }
            if (event.supplier && !existingVendors.has(event.supplier.toLowerCase())) {
                vendorAccumulator.add(event.supplier);
            }
            if (event.location && !existingLocations.has(event.location.toLowerCase())) {
                locationAccumulator.add(event.location);
            }
        });

        return {
            newSpecies: [...speciesAccumulator],
            newVendors: [...vendorAccumulator],
            newLocations: [...locationAccumulator],
        };
    }, [extractedEvents, locations, species, vendors]);

    const hasNewItems = newSpecies.length > 0 || newVendors.length > 0 || newLocations.length > 0;

    const renderCameraStep = () => (
        <Box
            sx={{
                position: 'relative',
                width: '100%',
                height: '100%',
                bgcolor: 'black',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
            }}
        >
            <video ref={videoRef} autoPlay playsInline style={{ width: '100%', height: '100%', objectFit: 'contain' }} />
            <Button
                onClick={handleTakePicture}
                variant="contained"
                color="primary"
                sx={{
                    position: 'absolute',
                    bottom: 32,
                    px: 4,
                    py: 1.5,
                    borderRadius: 999,
                    textTransform: 'none',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                }}
                startIcon={<CameraIcon />}
            >
                Capture
            </Button>
        </Box>
    );

    const renderPreviewStep = () => (
        <Box
            sx={{
                position: 'relative',
                width: '100%',
                height: '100%',
                bgcolor: 'black',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
            }}
        >
            {capturedImage && (
                <img src={capturedImage} alt="Captured" style={{ width: '100%', height: '100%', objectFit: 'contain' }} />
            )}
            {error && (
                <Alert severity="error" sx={{ position: 'absolute', top: 16, left: '50%', transform: 'translateX(-50%)', maxWidth: 420 }}>
                    {error}
                </Alert>
            )}
            <Stack direction="row" spacing={3} sx={{ position: 'absolute', bottom: 32 }}>
                <Button variant="outlined" color="inherit" onClick={() => setStep('camera')} sx={{ textTransform: 'none' }}>
                    Retake
                </Button>
                <Button variant="contained" color="primary" onClick={handleUsePicture} sx={{ textTransform: 'none' }}>
                    Use Picture
                </Button>
            </Stack>
        </Box>
    );

    const renderLoadingStep = () => (
        <Stack spacing={2} alignItems="center" justifyContent="center" sx={{ py: 6 }}>
            <CircularProgress size={40} />
            <Typography variant="h6" color="text.primary">
                Analyzing document…
            </Typography>
        </Stack>
    );

    const renderConfirmStep = () => (
        <Stack spacing={3} sx={{ p: { xs: 3, md: 4 }, maxHeight: '70vh', overflowY: 'auto' }}>
            <Typography variant="h6" fontWeight={700} color="text.primary">
                Confirm Extracted Data
            </Typography>
            {hasNewItems && (
                <Alert severity="info" sx={{ borderRadius: 2 }}>
                    <Typography variant="subtitle2" fontWeight={700} gutterBottom>
                        New items detected! These will be added to your database upon saving:
                    </Typography>
                    <Stack spacing={1} sx={{ mt: 1 }}>
                        {newSpecies.length > 0 && (
                            <Typography variant="body2">
                                <strong>Species:</strong> {newSpecies.join(', ')}
                            </Typography>
                        )}
                        {newVendors.length > 0 && (
                            <Typography variant="body2">
                                <strong>Vendors:</strong> {newVendors.join(', ')}
                            </Typography>
                        )}
                        {newLocations.length > 0 && (
                            <Typography variant="body2">
                                <strong>Locations:</strong> {newLocations.join(', ')}
                            </Typography>
                        )}
                    </Stack>
                </Alert>
            )}

            <Stack spacing={2.5}>
                {extractedEvents.map((event, index) => {
                    const isNewProduct = event.product && newSpecies.includes(event.product);
                    const isNewSupplier = event.supplier && newVendors.includes(event.supplier);
                    const isNewLocation = event.location && newLocations.includes(event.location);

                    return (
                        <Paper key={index} variant="outlined" sx={{ p: 2.5, borderRadius: 2, position: 'relative' }}>
                            <IconButton
                                onClick={() => removeEvent(index)}
                                size="small"
                                sx={{ position: 'absolute', top: 8, right: 8 }}
                                aria-label="Remove event from extracted list"
                            >
                                <TrashIcon />
                            </IconButton>
                            <Grid container spacing={2}>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Event Type"
                                        size="small"
                                        fullWidth
                                        value={event.eventType || ''}
                                        onChange={(e) => handleDataChange(index, 'eventType', e.target.value)}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Product"
                                        size="small"
                                        fullWidth
                                        value={event.product || ''}
                                        onChange={(e) => handleDataChange(index, 'product', e.target.value)}
                                        InputProps={
                                            isNewProduct
                                                ? {
                                                      endAdornment: (
                                                          <InputAdornment position="end">
                                                              <Chip label="New" color="success" size="small" />
                                                          </InputAdornment>
                                                      ),
                                                  }
                                                : undefined
                                        }
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Quantity"
                                        size="small"
                                        fullWidth
                                        type="number"
                                        value={event.quantity ?? ''}
                                        onChange={(e) => handleDataChange(index, 'quantity', Number.parseFloat(e.target.value))}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Supplier"
                                        size="small"
                                        fullWidth
                                        value={event.supplier || ''}
                                        onChange={(e) => handleDataChange(index, 'supplier', e.target.value)}
                                        InputProps={
                                            isNewSupplier
                                                ? {
                                                      endAdornment: (
                                                          <InputAdornment position="end">
                                                              <Chip label="New" color="success" size="small" />
                                                          </InputAdornment>
                                                      ),
                                                  }
                                                : undefined
                                        }
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Location"
                                        size="small"
                                        fullWidth
                                        value={event.location || ''}
                                        onChange={(e) => handleDataChange(index, 'location', e.target.value)}
                                        InputProps={
                                            isNewLocation
                                                ? {
                                                      endAdornment: (
                                                          <InputAdornment position="end">
                                                              <Chip label="New" color="success" size="small" />
                                                          </InputAdornment>
                                                      ),
                                                  }
                                                : undefined
                                        }
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Date"
                                        size="small"
                                        fullWidth
                                        type="date"
                                        value={event.date || ''}
                                        onChange={(e) => handleDataChange(index, 'date', e.target.value)}
                                        InputLabelProps={{ shrink: true }}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Temperature"
                                        size="small"
                                        fullWidth
                                        type="number"
                                        value={event.temperature ?? ''}
                                        onChange={(e) => handleDataChange(index, 'temperature', Number.parseFloat(e.target.value))}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Batch Number"
                                        size="small"
                                        fullWidth
                                        value={event.batchNumber || ''}
                                        onChange={(e) => handleDataChange(index, 'batchNumber', e.target.value)}
                                    />
                                </Grid>
                                <Grid item xs={12}>
                                    <TextField
                                        label="Notes"
                                        size="small"
                                        fullWidth
                                        multiline
                                        minRows={2}
                                        value={event.notes || ''}
                                        onChange={(e) => handleDataChange(index, 'notes', e.target.value)}
                                    />
                                </Grid>
                            </Grid>
                        </Paper>
                    );
                })}
                {extractedEvents.length === 0 && (
                    <Alert severity="warning" variant="outlined" sx={{ borderRadius: 2 }}>
                        No events were extracted. Capture another photo or close the scanner.
                    </Alert>
                )}
            </Stack>
        </Stack>
    );

    const renderContent = () => {
        switch (step) {
            case 'camera':
                return renderCameraStep();
            case 'preview':
                return renderPreviewStep();
            case 'loading':
                return renderLoadingStep();
            case 'confirm':
                return renderConfirmStep();
            default:
                return null;
        }
    };

    const handleClose = useCallback(() => {
        stopCamera();
        onClose();
    }, [onClose, stopCamera]);

    return (
        <Dialog open onClose={handleClose} fullScreen>
            <DialogTitle
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    borderBottom: `1px solid ${theme.palette.divider}`,
                }}
            >
                Document Scanner
                <IconButton onClick={handleClose} edge="end" aria-label="Close document scanner">
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent sx={{ p: 0, bgcolor: alpha(theme.palette.background.default, theme.palette.mode === 'dark' ? 0.9 : 0.6) }}>
                <Box sx={{ minHeight: '70vh', display: 'flex', flexDirection: 'column' }}>{renderContent()}</Box>
                <canvas ref={canvasRef} style={{ display: 'none' }} />
            </DialogContent>
            {step === 'confirm' && (
                <DialogActions sx={{ borderTop: `1px solid ${theme.palette.divider}`, px: 3, py: 2 }}>
                    <Button variant="contained" onClick={() => onSave(extractedEvents)} disabled={extractedEvents.length === 0} sx={{ textTransform: 'none' }}>
                        Save {extractedEvents.length} Items
                    </Button>
                </DialogActions>
            )}
        </Dialog>
    );
};

CameraModal.displayName = 'CameraModal';
