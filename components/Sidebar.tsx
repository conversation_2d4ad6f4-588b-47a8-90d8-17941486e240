import AssessmentIcon from '@mui/icons-material/Assessment';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import DashboardIcon from '@mui/icons-material/Dashboard';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import Inventory2Icon from '@mui/icons-material/Inventory2';
import MenuIcon from '@mui/icons-material/Menu';
import PinDropIcon from '@mui/icons-material/PinDrop';
import SettingsIcon from '@mui/icons-material/Settings';
import {
  Avatar,
  Box,
  Divider,
  Drawer,
  IconButton,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  useMediaQuery,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import React from 'react';

type ViewType = 'dashboard' | 'lot-tracking' | 'inventory' | 'haccp' | 'calendar' | 'reports' | 'settings';

interface SidebarProps {
  currentView: ViewType;
  onNavigate: (view: ViewType) => void;
  onCollapseChange?: (collapsed: boolean) => void;
}

const NAV_WIDTH = 264;
const COLLAPSED_WIDTH = 84;

export const Sidebar: React.FC<SidebarProps> = ({ currentView, onNavigate, onCollapseChange }) => {
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
  const [open, setOpen] = React.useState(isDesktop);

  React.useEffect(() => {
    setOpen(isDesktop);
  }, [isDesktop]);

  React.useEffect(() => {
    onCollapseChange?.(!open);
  }, [open, onCollapseChange]);

  const drawerWidth = open ? NAV_WIDTH : COLLAPSED_WIDTH;

  const navItems: Array<{ label: string; value: ViewType; icon: React.ReactElement }> = [
    { label: 'Dashboard', value: 'dashboard', icon: <DashboardIcon /> },
    { label: 'Lot Tracking', value: 'lot-tracking', icon: <PinDropIcon /> },
    { label: 'Inventory', value: 'inventory', icon: <Inventory2Icon /> },
    { label: 'HACCP Logs', value: 'haccp', icon: <HealthAndSafetyIcon /> },
    { label: 'Calendar', value: 'calendar', icon: <CalendarMonthIcon /> },
    { label: 'Reports', value: 'reports', icon: <AssessmentIcon /> },
    { label: 'Settings', value: 'settings', icon: <SettingsIcon /> },
  ];

  const drawerContent = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        bgcolor: 'background.paper',
      }}
    >
      <Toolbar sx={{ justifyContent: open ? 'space-between' : 'center', px: 1.5 }}>
        <IconButton
          onClick={() => setOpen((prev) => !prev)}
          aria-label={open ? 'Collapse navigation' : 'Expand navigation'}
          edge="start"
        >
          <MenuIcon />
        </IconButton>
        {open && (
          <Typography variant="subtitle1" fontWeight={700}>
            HACCP Helper
          </Typography>
        )}
      </Toolbar>

      <Divider />

      <Box sx={{ px: open ? 2 : 1, py: 3 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: open ? 'row' : 'column',
            alignItems: 'center',
            gap: 2,
            transition: theme.transitions.create(['gap']),
          }}
        >
          <Avatar
            alt="Atlantic Seafoods"
            src="https://lh3.googleusercontent.com/aida-public/AB6AXuD62ZxrENcl7YNKEE-vAvUj-yiHmqQnR7RnjPLN9dlQftGZlrzUiSjqj3a3eoWOnLNGJoPZbxGz5ES_LAi5qsNwTPfQKTUXwuQUwvmR1JluCvopQlCZo-t0EAkDtnxDNMfWbqPZbni5-WQk5xsZHwbk86ldgad0_ToTjBnaNAHO3mj8gqENo-CCfdmoWe9Cd7ntzrc4Ej0a3lbduE1rOGC3xt-R4bfOo200ocVxd3pa6fDzGVWb82vg3d-aXIRJACwkYW_Ao14zu9k"
            sx={{ width: 44, height: 44 }}
          />
          {open && (
            <Box>
              <Typography variant="subtitle1" fontWeight={700}>
                Atlantic Seafoods
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Admin
              </Typography>
            </Box>
          )}
        </Box>
      </Box>

      <Divider />

      <List sx={{ flex: 1 }}>
        {navItems.map((item) => (
          <ListItemButton
            key={item.value}
            selected={currentView === item.value}
            onClick={() => {
              onNavigate(item.value);
              if (!isDesktop) {
                setOpen(false);
              }
            }}
            sx={{
              borderRadius: 2,
              mx: open ? 1 : 0.5,
              mb: 0.5,
            }}
          >
            <ListItemIcon
              sx={{
                minWidth: open ? 40 : 'auto',
                justifyContent: 'center',
              }}
            >
              {item.icon}
            </ListItemIcon>
            {open && (
              <ListItemText primary={item.label} primaryTypographyProps={{ noWrap: true }} />
            )}
          </ListItemButton>
        ))}
      </List>
    </Box>
  );

  return (
    <Drawer
      variant={isDesktop ? 'permanent' : 'temporary'}
      open={open}
      onClose={() => setOpen(false)}
      ModalProps={{ keepMounted: true }}
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          overflowX: 'hidden',
          transition: theme.transitions.create('width'),
        },
      }}
    >
      {drawerContent}
    </Drawer>
  );
};
