import React, { useEffect, useState } from 'react';
import {
    Box,
    Button,
    FormControl,
    Grid,
    InputLabel,
    MenuItem,
    Paper,
    Select,
    Stack,
    TextField,
    Typography,
} from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import { collection, addDoc, updateDoc, doc, serverTimestamp, deleteDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import { PurchaseOrder, Vendor, Species, EmailContext } from '../types';
import { ClipboardDocumentListIcon, PlusIcon, TrashIcon } from './IconComponents';

interface OrdersViewProps {
    purchaseOrders: PurchaseOrder[];
    vendors: Vendor[];
    species: Species[];
    onDraftEmail: (context: EmailContext) => void;
}

export const OrdersView: React.FC<OrdersViewProps> = ({ purchaseOrders, vendors, species, onDraftEmail }) => {
    const theme = useTheme();
    const [isFormOpen, setIsFormOpen] = useState(false);
    const [vendorId, setVendorId] = useState('');
    const [speciesName, setSpeciesName] = useState('');
    const [quantity, setQuantity] = useState('');
    const [expectedDeliveryDate, setExpectedDeliveryDate] = useState(new Date().toISOString().split('T')[0]);

    useEffect(() => {
        if (vendors.length > 0 && !vendorId) {
            setVendorId(vendors[0].id);
        }
        if (species.length > 0 && !speciesName) {
            setSpeciesName(species[0].name);
        }
    }, [vendors, species]);

    const resetForm = () => {
        setVendorId(vendors.length > 0 ? vendors[0].id : '');
        setSpeciesName(species.length > 0 ? species[0].name : '');
        setQuantity('');
        setExpectedDeliveryDate(new Date().toISOString().split('T')[0]);
        setIsFormOpen(false);
    };

    const handleSubmit = async (event: React.FormEvent) => {
        event.preventDefault();
        const selectedVendor = vendors.find((vendor) => vendor.id === vendorId);
        if (!vendorId || !speciesName || !quantity || !expectedDeliveryDate || !selectedVendor) {
            alert('All fields are required.');
            return;
        }

        const orderData = {
            vendorId,
            vendorName: selectedVendor.name,
            species: speciesName,
            quantity: Number.parseFloat(quantity),
            unit: 'lbs',
            expectedDeliveryDate,
            status: 'planning' as PurchaseOrder['status'],
            createdAt: serverTimestamp(),
        };

        try {
            await addDoc(collection(db, 'purchaseOrders'), orderData);
            resetForm();
        } catch (submitError) {
            console.error('Error creating purchase order: ', submitError);
            alert('Failed to create purchase order.');
        }
    };

    const updateOrderStatus = async (orderId: string, status: 'ordered' | 'received') => {
        const orderRef = doc(db, 'purchaseOrders', orderId);
        await updateDoc(orderRef, { status });
    };

    const deleteOrder = async (orderId: string) => {
        if (window.confirm('Are you sure you want to delete this planned order?')) {
            await deleteDoc(doc(db, 'purchaseOrders', orderId));
        }
    };

    const today = new Date();
    const ordersToPlace = purchaseOrders.filter((order) => {
        const vendor = vendors.find((v) => v.id === order.vendorId);
        if (order.status !== 'planning' || !vendor?.sourcingLeadTimeDays) return false;
        const deliveryDate = new Date(`${order.expectedDeliveryDate}T00:00:00`);
        const orderDate = new Date(deliveryDate);
        orderDate.setDate(deliveryDate.getDate() - vendor.sourcingLeadTimeDays);
        return today >= orderDate;
    });

    const plannedOrders = purchaseOrders.filter(
        (order) => order.status === 'planning' && !ordersToPlace.includes(order),
    );
    const pendingOrders = purchaseOrders.filter((order) => order.status === 'ordered');
    const receivedOrders = purchaseOrders.filter((order) => order.status === 'received');

    const iconContainerSx = {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        '& svg': {
            width: 20,
            height: 20,
        },
    } as const;

    const OrderCard: React.FC<{ order: PurchaseOrder; showActions?: boolean }> = ({ order, showActions = false }) => {
        const vendor = vendors.find((v) => v.id === order.vendorId);
        return (
            <Paper
                variant="outlined"
                sx={{
                    p: 2.5,
                    borderRadius: 2,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1.5,
                    bgcolor: alpha(theme.palette.background.paper, theme.palette.mode === 'dark' ? 0.2 : 0.04),
                }}
            >
                <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                    <Box>
                        <Typography variant="subtitle1" fontWeight={700} color="text.primary">
                            {order.species}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            {order.quantity} {order.unit}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                            from {order.vendorName}
                        </Typography>
                    </Box>
                    <Typography variant="body2" fontWeight={600} color="text.secondary">
                        Due: {order.expectedDeliveryDate}
                    </Typography>
                </Stack>
                {showActions && (
                    <Stack direction="row" justifyContent="flex-end" spacing={1.5} sx={{ pt: 1.5, borderTop: `1px solid ${theme.palette.divider}` }}>
                        <Button
                            variant="text"
                            color="error"
                            size="small"
                            onClick={() => deleteOrder(order.id)}
                            startIcon={
                                <Box sx={{ ...iconContainerSx, color: 'inherit' }}>
                                    <TrashIcon />
                                </Box>
                            }
                        >
                            Delete
                        </Button>
                        {vendor && (
                            <Button
                                variant="text"
                                color="primary"
                                size="small"
                                onClick={() => onDraftEmail({ type: 'order', order, vendor })}
                                sx={{ textTransform: 'none', fontWeight: 600 }}
                            >
                                Draft Email
                            </Button>
                        )}
                        {order.status === 'planning' && (
                            <Button
                                variant="contained"
                                color="primary"
                                size="small"
                                onClick={() => updateOrderStatus(order.id, 'ordered')}
                                sx={{ textTransform: 'none' }}
                            >
                                Mark Ordered
                            </Button>
                        )}
                        {order.status === 'ordered' && (
                            <Button
                                variant="contained"
                                color="success"
                                size="small"
                                onClick={() => updateOrderStatus(order.id, 'received')}
                                sx={{ textTransform: 'none' }}
                            >
                                Mark Received
                            </Button>
                        )}
                    </Stack>
                )}
            </Paper>
        );
    };

    return (
        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Paper variant="outlined" sx={{ p: { xs: 3, md: 4 }, borderRadius: 3 }}>
                <Stack
                    direction={{ xs: 'column', sm: 'row' }}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems={{ xs: 'flex-start', sm: 'center' }}
                >
                    <Stack direction="row" spacing={2} alignItems="center">
                        <Box
                            sx={{
                                ...iconContainerSx,
                                width: 48,
                                height: 48,
                                borderRadius: 2,
                                bgcolor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.35 : 0.12),
                                color: theme.palette.primary.main,
                            }}
                        >
                            <ClipboardDocumentListIcon />
                        </Box>
                        <Typography variant="h4" fontWeight={700} color="text.primary">
                            Purchase Orders
                        </Typography>
                    </Stack>
                    <Button
                        variant={isFormOpen ? 'outlined' : 'contained'}
                        color="primary"
                        startIcon={
                            <Box sx={{ ...iconContainerSx, color: 'inherit' }}>
                                <PlusIcon />
                            </Box>
                        }
                        onClick={() => setIsFormOpen((prev) => !prev)}
                        sx={{ textTransform: 'none' }}
                    >
                        {isFormOpen ? 'Close Form' : 'Plan New Order'}
                    </Button>
                </Stack>

                {isFormOpen && (
                    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 4 }}>
                        <Grid container spacing={2.5}>
                            <Grid item xs={12} md={6}>
                                <FormControl fullWidth>
                                    <InputLabel id="vendor-select-label">Vendor</InputLabel>
                                    <Select
                                        labelId="vendor-select-label"
                                        value={vendorId}
                                        label="Vendor"
                                        onChange={(event) => setVendorId(event.target.value)}
                                        required
                                    >
                                        {vendors.map((vendor) => (
                                            <MenuItem key={vendor.id} value={vendor.id}>
                                                {vendor.name}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <FormControl fullWidth>
                                    <InputLabel id="species-select-label">Species</InputLabel>
                                    <Select
                                        labelId="species-select-label"
                                        value={speciesName}
                                        label="Species"
                                        onChange={(event) => setSpeciesName(event.target.value)}
                                        required
                                    >
                                        {species.map((entry) => (
                                            <MenuItem key={entry.id} value={entry.name}>
                                                {entry.name}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField
                                    type="number"
                                    label="Quantity (lbs)"
                                    fullWidth
                                    required
                                    value={quantity}
                                    onChange={(event) => setQuantity(event.target.value)}
                                    inputProps={{ min: 0, step: 0.1 }}
                                />
                            </Grid>
                            <Grid item xs={12} md={6}>
                                <TextField
                                    type="date"
                                    label="Expected Delivery Date"
                                    fullWidth
                                    required
                                    value={expectedDeliveryDate}
                                    onChange={(event) => setExpectedDeliveryDate(event.target.value)}
                                    InputLabelProps={{ shrink: true }}
                                />
                            </Grid>
                            <Grid item xs={12}>
                                <Stack direction="row" spacing={2} justifyContent="flex-end">
                                    <Button variant="outlined" color="inherit" onClick={resetForm} sx={{ textTransform: 'none' }}>
                                        Cancel
                                    </Button>
                                    <Button type="submit" variant="contained" color="primary" sx={{ textTransform: 'none' }}>
                                        Save Plan
                                    </Button>
                                </Stack>
                            </Grid>
                        </Grid>
                    </Box>
                )}
            </Paper>

            <Grid container spacing={3}>
                <Grid item xs={12} md={6} lg={4}>
                    <Paper variant="outlined" sx={{ p: 3, borderRadius: 2, height: '100%', display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Typography variant="h6" color="error.main" fontWeight={700}>
                            Action Required: Place Orders
                        </Typography>
                        <Stack spacing={2}>
                            {ordersToPlace.length > 0 ? (
                                ordersToPlace.map((order) => (
                                    <OrderCard key={order.id} order={order} showActions />
                                ))
                            ) : (
                                <Typography variant="body2" color="text.secondary">
                                    No orders require immediate action.
                                </Typography>
                            )}
                        </Stack>
                    </Paper>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                    <Paper variant="outlined" sx={{ p: 3, borderRadius: 2, height: '100%', display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Typography variant="h6" color="text.primary" fontWeight={700}>
                            Planned Orders
                        </Typography>
                        <Stack spacing={2}>
                            {plannedOrders.length > 0 ? (
                                plannedOrders.map((order) => (
                                    <OrderCard key={order.id} order={order} showActions />
                                ))
                            ) : (
                                <Typography variant="body2" color="text.secondary">
                                    No orders currently planned.
                                </Typography>
                            )}
                        </Stack>
                    </Paper>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                    <Paper variant="outlined" sx={{ p: 3, borderRadius: 2, height: '100%', display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Typography variant="h6" color="text.primary" fontWeight={700}>
                            Pending Delivery
                        </Typography>
                        <Stack spacing={2}>
                            {pendingOrders.length > 0 ? (
                                pendingOrders.map((order) => (
                                    <OrderCard key={order.id} order={order} showActions />
                                ))
                            ) : (
                                <Typography variant="body2" color="text.secondary">
                                    No orders pending delivery.
                                </Typography>
                            )}
                        </Stack>
                    </Paper>
                </Grid>
                <Grid item xs={12}>
                    <Paper variant="outlined" sx={{ p: 3, borderRadius: 2 }}>
                        <Typography variant="h6" fontWeight={700} color="text.primary" gutterBottom>
                            Recently Received
                        </Typography>
                        <Stack spacing={2}>
                            {receivedOrders.length > 0 ? (
                                receivedOrders.map((order) => <OrderCard key={order.id} order={order} />)
                            ) : (
                                <Typography variant="body2" color="text.secondary">
                                    No orders have been marked as received.
                                </Typography>
                            )}
                        </Stack>
                    </Paper>
                </Grid>
            </Grid>
        </Box>
    );
};

OrdersView.displayName = 'OrdersView';
