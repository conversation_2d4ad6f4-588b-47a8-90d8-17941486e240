
import { Box, Paper, Stack, Typography } from '@mui/material';
import React from 'react';
import { ConversationTurn } from '../types';

interface ConversationLogProps {
  turns: ConversationTurn[];
}

export const ConversationLog: React.FC<ConversationLogProps> = ({ turns }) => (
  <Box
    sx={{
      bgcolor: (theme) =>
        theme.palette.mode === 'light' ? 'rgba(226, 232, 240, 0.65)' : 'rgba(15, 23, 42, 0.65)',
      borderRadius: 3,
      boxShadow: (theme) =>
        theme.palette.mode === 'light' ? '0px 12px 32px rgba(15, 23, 42, 0.12)' : '0px 14px 36px rgba(2, 6, 23, 0.65)',
      border: (theme) => `1px solid ${theme.palette.divider}`,
      backdropFilter: 'blur(12px)',
      p: 3,
      height: 256,
      overflowY: 'auto',
      display: 'flex',
      flexDirection: 'column-reverse',
    }}
  >
    <Stack spacing={2}>
      {turns.map((turn) => (
        <Box
          key={turn.id}
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: turn.speaker === 'user' ? 'flex-start' : 'flex-end',
            gap: 0.5,
          }}
        >
          <Paper
            elevation={0}
            sx={{
              px: 2,
              py: 1.5,
              maxWidth: 320,
              bgcolor: (theme) =>
                turn.speaker === 'user' ? theme.palette.primary.main : theme.palette.grey[700],
              color: (theme) => (turn.speaker === 'user' ? theme.palette.primary.contrastText : theme.palette.common.white),
            }}
          >
            <Typography variant="body2">{turn.text}</Typography>
          </Paper>
          <Typography
            variant="caption"
            sx={{ color: (theme) => theme.palette.text.secondary, textTransform: 'capitalize' }}
          >
            {turn.speaker}
          </Typography>
        </Box>
      ))}
      {turns.length === 0 && (
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 8 }}>
          <Typography variant="body2" color="text.secondary">
            Conversation will appear here.
          </Typography>
        </Box>
      )}
    </Stack>
  </Box>
);
