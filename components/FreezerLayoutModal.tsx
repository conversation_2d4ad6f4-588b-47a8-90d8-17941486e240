

import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { alpha, useTheme } from '@mui/material/styles';
import { Location } from '../types';
import { CubeIcon } from './IconComponents';

interface FreezerLayoutModalProps {
  isOpen: boolean;
  onClose: () => void;
  location: Location;
  inventory: { [product: string]: number };
}

export const FreezerLayoutModal: React.FC<FreezerLayoutModalProps> = ({ isOpen, onClose, location, inventory }) => {
  const theme = useTheme();
  const inventoryItems = Object.entries(inventory).filter(([, qty]) => (qty as number) > 0.01);

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        Layout for: {location.name}
        <IconButton onClick={onClose} edge="end" aria-label="Close freezer layout dialog">
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers sx={{ p: { xs: 3, md: 4 } }}>
        {location.description && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            {location.description}
          </Typography>
        )}
        {inventoryItems.length > 0 ? (
          <Grid container spacing={3}>
            {inventoryItems.map(([product, quantity]) => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={product}>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 3,
                    borderRadius: 2,
                    height: 180,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    gap: 1.5,
                    bgcolor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.12 : 0.06),
                  }}
                >
                  <Box sx={{ color: theme.palette.primary.main, '& svg': { width: 40, height: 40 } }}>
                    <CubeIcon />
                  </Box>
                  <Box>
                    <Typography variant="subtitle1" fontWeight={700} color="text.primary" sx={{ wordBreak: 'break-word' }}>
                      {product}
                    </Typography>
                    <Typography variant="h6" fontWeight={800} color="primary.main">
                      {(quantity as number).toFixed(2)} lbs
                    </Typography>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Paper variant="outlined" sx={{ p: 6, borderRadius: 2, textAlign: 'center' }}>
            <Typography variant="body1" color="text.secondary">
              This location is currently empty.
            </Typography>
          </Paper>
        )}
      </DialogContent>
    </Dialog>
  );
};
