import React, { useCallback, useEffect, useMemo, useState } from 'react';
import * as xlsx from 'xlsx';
import { GoogleGenAI, Type } from '@google/genai';
import {
    Alert,
    Box,
    Button,
    Chip,
    CircularProgress,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    Grid,
    IconButton,
    InputAdornment,
    InputLabel,
    MenuItem,
    Paper,
    Select,
    Stack,
    TextField,
    Typography,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { alpha, useTheme } from '@mui/material/styles';
import { HACCPEvent, Species, Vendor, Location, EventType } from '../types';
import { DocumentArrowUpIcon, TrashIcon } from './IconComponents';

interface ImportModalProps {
    species: Species[];
    vendors: Vendor[];
    locations: Location[];
    onClose: () => void;
    onSave: (events: Partial<HACCPEvent>[]) => void;
}

type ModalStep = 'upload' | 'loading' | 'confirm' | 'error';

type TempHACCPEvent = Partial<HACCPEvent> & { tempId: number };

const eventTypeOptions: Array<{ label: string; value: EventType | 'auto' }> = [
    { label: 'Auto-Detect (from file)', value: 'auto' },
    { label: 'Receiving', value: 'receiving' },
    { label: 'Sales', value: 'sales' },
    { label: 'Disposal', value: 'disposal' },
    { label: 'Relocation', value: 'relocation' },
    { label: 'Re-sealing', value: 're-sealing' },
    { label: 'Inventory Check', value: 'inventory' },
    { label: 'Sanitation', value: 'sanitation' },
];

export const ImportModal: React.FC<ImportModalProps> = ({ species, vendors, locations, onClose, onSave }) => {
    const theme = useTheme();
    const [step, setStep] = useState<ModalStep>('upload');
    const [originalEvents, setOriginalEvents] = useState<TempHACCPEvent[]>([]);
    const [extractedEvents, setExtractedEvents] = useState<TempHACCPEvent[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [overrideEventType, setOverrideEventType] = useState<EventType | 'auto'>('auto');

    const handleFile = useCallback(
        async (file: File) => {
            setStep('loading');
            setError(null);
            setOverrideEventType('auto');

            const geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY;
            if (!geminiApiKey) {
                setError('Gemini API key is not configured. Please add VITE_GEMINI_API_KEY to your environment settings.');
                setStep('error');
                return;
            }

            try {
                const data = await file.arrayBuffer();
                const workbook = xlsx.read(data);
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const json: any[] = xlsx.utils.sheet_to_json(worksheet);

                if (json.length === 0) {
                    setError('The uploaded file is empty or in an unsupported format.');
                    setStep('error');
                    return;
                }

                const headers = Object.keys(json[0]).join(', ');
                const fullDataString = json.map((row) => JSON.stringify(row)).join('\n');

                const ai = new GoogleGenAI({ apiKey: geminiApiKey });

                const response = await ai.models.generateContent({
                    model: 'gemini-2.5-flash',
                    contents: {
                        parts: [
                            {
                                text: `Analyze the following data extracted from a user's spreadsheet and convert it into a JSON array of HACCP events.

File Headers: ${headers}
Spreadsheet Data:
${fullDataString}

Existing database values for matching:
- Species: ${species.map((s) => s.name).join(', ')}
- Vendors: ${vendors.map((v) => v.name).join(', ')}
- Locations: ${locations.map((l) => l.name).join(', ')}

Your task is to map the spreadsheet columns to the fields in the provided JSON schema.
- Infer the 'eventType' from the data (e.g., if there's a supplier, it's likely 'receiving'). Default to 'inventory' if unsure.
- If a date is present, use it in YYYY-MM-DD format. Standardize date formats if possible (e.g., MM/DD/YY -> YYYY-MM-DD). If no year is present assume the current year.
- Ensure 'quantity' and 'temperature' are numbers.
- Be precise. If a value for a field isn't in the data, omit that key from the object.
- Your output MUST be ONLY the JSON array.
`,
                            },
                        ],
                    },
                    config: {
                        responseMimeType: 'application/json',
                        responseSchema: {
                            type: Type.ARRAY,
                            items: {
                                type: Type.OBJECT,
                                properties: {
                                    eventType: {
                                        type: Type.STRING,
                                        enum: ['receiving', 'inventory', 'disposal', 'sales', 'sanitation', 'relocation', 're-sealing'],
                                    },
                                    product: { type: Type.STRING },
                                    productForm: { type: Type.STRING },
                                    quantity: { type: Type.NUMBER },
                                    unit: { type: Type.STRING },
                                    supplier: { type: Type.STRING },
                                    location: { type: Type.STRING },
                                    date: { type: Type.STRING },
                                    batchNumber: { type: Type.STRING },
                                    temperature: { type: Type.NUMBER },
                                    notes: { type: Type.STRING },
                                },
                            },
                        },
                    },
                });

                const potentialText = (response as any).text ?? (response as any).response?.text;
                const rawResponse =
                    typeof potentialText === 'function' ? await potentialText.call((response as any).response ?? response) : potentialText;
                if (!rawResponse || typeof rawResponse !== 'string') {
                    throw new Error('Gemini returned an empty response.');
                }

                const parsedEvents = JSON.parse(rawResponse).map((event: Partial<HACCPEvent>, index: number) => ({
                    ...event,
                    tempId: index,
                }));

                setOriginalEvents(parsedEvents);
                setExtractedEvents(parsedEvents);
                setStep('confirm');
            } catch (err: any) {
                console.error('Error processing file:', err);
                if (err.message?.includes('permission') || err.message?.includes('not found')) {
                    setError('API key error. Please close and re-select your API key from the main screen.');
                } else {
                    setError('AI analysis failed. Confirm the file is a standard CSV or Excel format and try again.');
                }
                setStep('error');
            }
        },
        [locations, species, vendors],
    );

    useEffect(() => {
        if (step !== 'confirm' || originalEvents.length === 0) return;

        if (overrideEventType === 'auto') {
            setExtractedEvents((currentEvents) =>
                currentEvents.map((event) => {
                    const originalEvent = originalEvents.find((original) => original.tempId === event.tempId);
                    return {
                        ...event,
                        eventType: originalEvent ? originalEvent.eventType : 'inventory',
                    };
                }),
            );
        } else {
            setExtractedEvents((currentEvents) =>
                currentEvents.map((event) => ({
                    ...event,
                    eventType: overrideEventType,
                })),
            );
        }
    }, [overrideEventType, originalEvents, step]);

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            handleFile(event.target.files[0]);
        }
    };

    const handleDragOver = (event: React.DragEvent) => {
        event.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = (event: React.DragEvent) => {
        event.preventDefault();
        setIsDragging(false);
    };

    const handleDrop = (event: React.DragEvent) => {
        event.preventDefault();
        setIsDragging(false);
        if (event.dataTransfer.files && event.dataTransfer.files[0]) {
            handleFile(event.dataTransfer.files[0]);
        }
    };

    const handleDataChange = (id: number, field: keyof HACCPEvent, value: string | number) => {
        setExtractedEvents((currentEvents) =>
            currentEvents.map((event) => (event.tempId === id ? { ...event, [field]: value } : event)),
        );
    };

    const removeEvent = (id: number) => {
        setExtractedEvents((prev) => prev.filter((event) => event.tempId !== id));
        setOriginalEvents((prev) => prev.filter((event) => event.tempId !== id));
    };

    const { newSpecies, newVendors, newLocations } = useMemo(() => {
        const existingSpecies = new Set(species.map((entry) => entry.name.toLowerCase()));
        const existingVendors = new Set(vendors.map((entry) => entry.name.toLowerCase()));
        const existingLocations = new Set(locations.map((entry) => entry.name.toLowerCase()));

        const speciesAccumulator = new Set<string>();
        const vendorAccumulator = new Set<string>();
        const locationAccumulator = new Set<string>();

        extractedEvents.forEach((event) => {
            if (event.product && !existingSpecies.has(event.product.toLowerCase())) {
                speciesAccumulator.add(event.product);
            }
            if (event.supplier && !existingVendors.has(event.supplier.toLowerCase())) {
                vendorAccumulator.add(event.supplier);
            }
            if (event.location && !existingLocations.has(event.location.toLowerCase())) {
                locationAccumulator.add(event.location);
            }
        });

        return {
            newSpecies: [...speciesAccumulator],
            newVendors: [...vendorAccumulator],
            newLocations: [...locationAccumulator],
        };
    }, [extractedEvents, locations, species, vendors]);

    const hasNewItems = newSpecies.length > 0 || newVendors.length > 0 || newLocations.length > 0;

    const renderUploadStep = () => (
        <Stack spacing={3} alignItems="center" justifyContent="center" sx={{ py: 6, textAlign: 'center' }}>
            <Box
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                sx={{
                    width: '100%',
                    border: '2px dashed',
                    borderColor: isDragging ? theme.palette.primary.main : alpha(theme.palette.text.secondary, 0.4),
                    borderRadius: 3,
                    p: { xs: 5, md: 7 },
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: 2,
                    transition: theme.transitions.create(['border-color', 'background-color']),
                    backgroundColor: isDragging
                        ? alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.2 : 0.08)
                        : 'transparent',
                }}
            >
                <Box sx={{ color: theme.palette.text.secondary, '& svg': { width: 64, height: 64 } }}>
                    <DocumentArrowUpIcon />
                </Box>
                <Typography variant="h6" fontWeight={700} color="text.primary">
                    Drag & drop your CSV or Excel file here
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    or
                </Typography>
                <Button variant="contained" component="label" sx={{ textTransform: 'none' }}>
                    Browse Files
                    <input
                        hidden
                        type="file"
                        accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                        onChange={handleFileSelect}
                    />
                </Button>
            </Box>
            <Typography variant="caption" color="text.secondary">
                Your data is processed locally in your browser.
            </Typography>
        </Stack>
    );

    const renderLoadingStep = () => (
        <Stack spacing={2} alignItems="center" justifyContent="center" sx={{ py: 6 }}>
            <CircularProgress size={40} />
            <Typography variant="h6" color="text.primary">
                Parsing file & analyzing data…
            </Typography>
        </Stack>
    );

    const renderErrorStep = () => (
        <Stack spacing={2} alignItems="center" sx={{ py: 6, textAlign: 'center' }}>
            <Alert severity="error" sx={{ maxWidth: 420 }}>
                <Typography variant="subtitle1" fontWeight={700} gutterBottom>
                    An error occurred
                </Typography>
                <Typography variant="body2">{error}</Typography>
            </Alert>
            <Button variant="contained" onClick={() => setStep('upload')} sx={{ textTransform: 'none' }}>
                Try Again
            </Button>
        </Stack>
    );

    const renderConfirmStep = () => (
        <Stack spacing={3} sx={{ p: { xs: 3, md: 4 }, maxHeight: '70vh', overflowY: 'auto' }}>
            <Typography variant="h6" fontWeight={700} color="text.primary">
                Confirm Imported Data
            </Typography>
            <Paper variant="outlined" sx={{ p: 2.5, borderRadius: 2 }}>
                <FormControl fullWidth size="small">
                    <InputLabel id="event-type-override-label">Set all events to type</InputLabel>
                    <Select
                        labelId="event-type-override-label"
                        value={overrideEventType}
                        label="Set all events to type"
                        onChange={(event) => setOverrideEventType(event.target.value as EventType | 'auto')}
                    >
                        {eventTypeOptions.map((option) => (
                            <MenuItem key={option.value} value={option.value}>
                                {option.label}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Paper>

            {hasNewItems && (
                <Alert severity="info" sx={{ borderRadius: 2 }}>
                    <Typography variant="subtitle2" fontWeight={700} gutterBottom>
                        New items detected! These will be added to your database upon saving:
                    </Typography>
                    <Stack spacing={1} sx={{ mt: 1 }}>
                        {newSpecies.length > 0 && (
                            <Typography variant="body2">
                                <strong>Species:</strong> {newSpecies.join(', ')}
                            </Typography>
                        )}
                        {newVendors.length > 0 && (
                            <Typography variant="body2">
                                <strong>Vendors:</strong> {newVendors.join(', ')}
                            </Typography>
                        )}
                        {newLocations.length > 0 && (
                            <Typography variant="body2">
                                <strong>Locations:</strong> {newLocations.join(', ')}
                            </Typography>
                        )}
                    </Stack>
                </Alert>
            )}

            <Stack spacing={2.5}>
                {extractedEvents.map((event) => {
                    const isNewProduct = event.product && newSpecies.includes(event.product);
                    const isNewSupplier = event.supplier && newVendors.includes(event.supplier);
                    const isNewLocation = event.location && newLocations.includes(event.location);

                    return (
                        <Paper
                            key={event.tempId}
                            variant="outlined"
                            sx={{ p: 2.5, borderRadius: 2, position: 'relative' }}
                        >
                            <IconButton
                                onClick={() => removeEvent(event.tempId)}
                                size="small"
                                sx={{ position: 'absolute', top: 8, right: 8 }}
                                aria-label="Remove event from import list"
                            >
                                <TrashIcon />
                            </IconButton>
                            <Grid container spacing={2}>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Event Type"
                                        size="small"
                                        fullWidth
                                        value={event.eventType || ''}
                                        onChange={(e) => handleDataChange(event.tempId, 'eventType', e.target.value)}
                                        disabled={overrideEventType !== 'auto'}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Product"
                                        size="small"
                                        fullWidth
                                        value={event.product || ''}
                                        onChange={(e) => handleDataChange(event.tempId, 'product', e.target.value)}
                                        InputProps={
                                            isNewProduct
                                                ? {
                                                      endAdornment: (
                                                          <InputAdornment position="end">
                                                              <Chip label="New" color="success" size="small" />
                                                          </InputAdornment>
                                                      ),
                                                  }
                                                : undefined
                                        }
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Quantity"
                                        size="small"
                                        fullWidth
                                        type="number"
                                        value={event.quantity ?? ''}
                                        onChange={(e) => handleDataChange(event.tempId, 'quantity', Number.parseFloat(e.target.value))}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Supplier"
                                        size="small"
                                        fullWidth
                                        value={event.supplier || ''}
                                        onChange={(e) => handleDataChange(event.tempId, 'supplier', e.target.value)}
                                        InputProps={
                                            isNewSupplier
                                                ? {
                                                      endAdornment: (
                                                          <InputAdornment position="end">
                                                              <Chip label="New" color="success" size="small" />
                                                          </InputAdornment>
                                                      ),
                                                  }
                                                : undefined
                                        }
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Location"
                                        size="small"
                                        fullWidth
                                        value={event.location || ''}
                                        onChange={(e) => handleDataChange(event.tempId, 'location', e.target.value)}
                                        InputProps={
                                            isNewLocation
                                                ? {
                                                      endAdornment: (
                                                          <InputAdornment position="end">
                                                              <Chip label="New" color="success" size="small" />
                                                          </InputAdornment>
                                                      ),
                                                  }
                                                : undefined
                                        }
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Date"
                                        size="small"
                                        fullWidth
                                        type="date"
                                        value={event.date || ''}
                                        onChange={(e) => handleDataChange(event.tempId, 'date', e.target.value)}
                                        InputLabelProps={{ shrink: true }}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Temperature"
                                        size="small"
                                        fullWidth
                                        type="number"
                                        value={event.temperature ?? ''}
                                        onChange={(e) => handleDataChange(event.tempId, 'temperature', Number.parseFloat(e.target.value))}
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={4}>
                                    <TextField
                                        label="Batch Number"
                                        size="small"
                                        fullWidth
                                        value={event.batchNumber || ''}
                                        onChange={(e) => handleDataChange(event.tempId, 'batchNumber', e.target.value)}
                                    />
                                </Grid>
                                <Grid item xs={12}>
                                    <TextField
                                        label="Notes"
                                        size="small"
                                        fullWidth
                                        multiline
                                        minRows={2}
                                        value={event.notes || ''}
                                        onChange={(e) => handleDataChange(event.tempId, 'notes', e.target.value)}
                                    />
                                </Grid>
                            </Grid>
                        </Paper>
                    );
                })}

                {extractedEvents.length === 0 && (
                    <Alert severity="warning" variant="outlined" sx={{ borderRadius: 2 }}>
                        No events remain. Upload another file or close the modal.
                    </Alert>
                )}
            </Stack>
        </Stack>
    );

    const renderContent = () => {
        switch (step) {
            case 'upload':
                return renderUploadStep();
            case 'loading':
                return renderLoadingStep();
            case 'error':
                return renderErrorStep();
            case 'confirm':
                return renderConfirmStep();
            default:
                return null;
        }
    };

    const handleModalClose = useCallback(() => {
        setStep('upload');
        setOriginalEvents([]);
        setExtractedEvents([]);
        setError(null);
        onClose();
    }, [onClose]);

    return (
        <Dialog open onClose={handleModalClose} maxWidth="lg" fullWidth>
            <DialogTitle
                sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', borderBottom: `1px solid ${theme.palette.divider}` }}
            >
                Import from File
                <IconButton onClick={handleModalClose} edge="end" aria-label="Close import dialog">
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent dividers sx={{ p: 0 }}>
                <Box>{renderContent()}</Box>
            </DialogContent>
            {step === 'confirm' && (
                <DialogActions sx={{ borderTop: `1px solid ${theme.palette.divider}`, px: 3, py: 2 }}>
                    <Button onClick={handleModalClose} color="inherit" sx={{ textTransform: 'none' }}>
                        Cancel
                    </Button>
                    <Button
                        onClick={() => onSave(extractedEvents)}
                        variant="contained"
                        disabled={extractedEvents.length === 0}
                        sx={{ textTransform: 'none' }}
                    >
                        Save {extractedEvents.length} Items
                    </Button>
                </DialogActions>
            )}
        </Dialog>
    );
};

ImportModal.displayName = 'ImportModal';
