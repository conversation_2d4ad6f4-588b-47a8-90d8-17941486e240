import React from 'react';
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';
import Typography from '@mui/material/Typography';

export const MockDataWarning: React.FC = () => (
    <Alert severity="warning" sx={{ alignItems: 'flex-start', borderRadius: 2 }}>
        <AlertTitle>Displaying Sample Data</AlertTitle>
        <Typography variant="body2" color="text.primary">
            Live data from the TempStick API cannot be fetched directly from the browser due to CORS security
            restrictions. This component is currently showing pre-loaded sample data.
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            <Typography component="span" variant="body2" sx={{ fontWeight: 600 }}>
                Developer Note:
            </Typography>{' '}
            To connect to the live API, route requests through a backend proxy that can append the required CORS
            headers, as described in the documentation.
        </Typography>
    </Alert>
);
