
interface StatusIndicatorProps {
  status: string;
  isRecording: boolean;
}

import { Box, Typography } from '@mui/material';
import { keyframes } from '@mui/material/styles';
import React from 'react';

const ping = keyframes`
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%,
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
`;

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status, isRecording }) => (
  <Box
    sx={{
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 1.5,
      px: 2.5,
      py: 1.5,
      borderRadius: 2,
      bgcolor: 'background.paper',
      border: (theme) => `1px solid ${theme.palette.divider}`,
      boxShadow: (theme) =>
        theme.palette.mode === 'light' ? '0px 6px 16px rgba(15, 23, 42, 0.12)' : '0px 8px 20px rgba(2, 6, 23, 0.45)',
      backdropFilter: 'blur(10px)',
    }}
  >
    {isRecording && (
      <Box
        sx={{
          position: 'relative',
          display: 'inline-flex',
          width: 12,
          height: 12,
          '&::before': {
            content: '""',
            position: 'absolute',
            inset: 0,
            borderRadius: '50%',
            bgcolor: (theme) => theme.palette.error.light,
            animation: `${ping} 1s cubic-bezier(0, 0, 0.2, 1) infinite`,
          },
        }}
      >
        <Box
          sx={{
            width: '100%',
            height: '100%',
            borderRadius: '50%',
            bgcolor: (theme) => theme.palette.error.main,
            position: 'relative',
          }}
        />
      </Box>
    )}
    <Typography variant="body1" color="text.primary" fontWeight={500}>
      {status}
    </Typography>
  </Box>
);
