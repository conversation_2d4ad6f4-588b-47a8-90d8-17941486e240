import AddIcon from '@mui/icons-material/Add';
import ArchiveOutlinedIcon from '@mui/icons-material/ArchiveOutlined';
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import FilterListIcon from '@mui/icons-material/FilterList';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import {
  Avatar,
  Box,
  Button,
  Checkbox,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  MenuItem,
  Pagination,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useMemo, useState } from 'react';
import { HACCPEvent, Species, Vendor } from '../types';
import { AddProductModal } from './AddProductModal';

interface InventoryManagementProps {
  events: HACCPEvent[];
  species: Species[];
  vendors: Vendor[];
}

type InventoryItem = {
  id: string;
  name: string;
  category?: 'Fish' | 'Shellfish' | 'Other';
  stock: number;
  unit?: string;
  unitPrice?: number;
  status: 'In Stock' | 'Low Stock' | 'Out of Stock';
  supplier?: string;
  dateAdded?: string;
  imageUrl?: string;
};

const PLACEHOLDER_IMAGE = 'https://via.placeholder.com/150';

type InventoryEditDraft = {
  name: string;
  category: InventoryItem['category'] | '';
  stock: string;
  unitPrice: string;
  status: InventoryItem['status'];
  supplier: string;
  dateAdded: string;
};

const InventoryManagement: React.FC<InventoryManagementProps> = ({ events, species }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [archivedIds, setArchivedIds] = useState<Set<string>>(new Set());
  const [inventoryOverrides, setInventoryOverrides] = useState<Record<string, InventoryItem>>({});
  const [editingItem, setEditingItem] = useState<InventoryItem | null>(null);
  const [editDraft, setEditDraft] = useState<InventoryEditDraft | null>(null);
  const [viewingItem, setViewingItem] = useState<InventoryItem | null>(null);
  const [archiveCandidate, setArchiveCandidate] = useState<InventoryItem | null>(null);
  const itemsPerPage = 10;

  const baseInventory = useMemo<InventoryItem[]>(() => {
    const inventoryMap: { [productName: string]: InventoryItem } = {};

    // Initialize with all species to ensure products with 0 stock are shown
    species.forEach(s => {
      inventoryMap[s.name] = {
        id: s.id,
        name: s.name,
        category: s.category,
        stock: 0,
        unit: 'lbs',
        status: 'Out of Stock',
        imageUrl: s.imageUrl,
      };
    });

    // Process events to calculate stock and other details
    [...events]
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .forEach(event => {
        if (!event.product || !inventoryMap[event.product]) return;

        const item = inventoryMap[event.product];
        const quantity = event.quantity || 0;

        switch (event.eventType) {
          case 'receiving':
            item.stock += quantity;
            item.supplier = event.supplier;
            item.unitPrice = event.unitPrice;
            item.dateAdded = event.date;
            item.unit = event.unit || item.unit || 'lbs';
            break;
          case 'sales':
          case 'disposal':
            item.stock -= quantity;
            item.unit = event.unit || item.unit || 'lbs';
            break;
        }
      });

    // Set status based on final stock count
    Object.values(inventoryMap).forEach(item => {
      if (item.stock <= 0) {
        item.status = 'Out of Stock';
        item.stock = 0; // Ensure stock is not negative
      } else if (item.stock < 50) {
        item.status = 'Low Stock';
      } else {
        item.status = 'In Stock';
      }
    });

    return Object.values(inventoryMap);
  }, [events, species]);

  const adjustedInventory = useMemo<InventoryItem[]>(() => {
    return baseInventory
      .map(item => inventoryOverrides[item.id] ?? item)
      .filter(item => !archivedIds.has(item.id));
  }, [archivedIds, baseInventory, inventoryOverrides]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return adjustedInventory.slice(startIndex, startIndex + itemsPerPage);
  }, [adjustedInventory, currentPage]);

  const totalPages = Math.ceil(Math.max(1, adjustedInventory.length) / itemsPerPage);

  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(totalPages || 1);
    }
  }, [currentPage, totalPages]);

  const handleExport = () => {
    const headers = ['Product Name', 'Category', 'Stock', 'Unit', 'Unit Price', 'Status', 'Supplier', 'Date Added'];
    const csvContent = [
      headers.join(','),
      ...adjustedInventory.map(item =>
        [
          item.name,
          item.category || 'N/A',
          item.stock.toFixed(2),
          item.unit || 'lbs',
          item.unitPrice?.toFixed(2) || 'N/A',
          item.status,
          item.supplier || 'N/A',
          item.dateAdded || 'N/A',
        ].join(','),
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.href) {
      URL.revokeObjectURL(link.href);
    }
    link.href = URL.createObjectURL(blob);
    link.download = 'inventory_data.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleAddProduct = (newSpecies: Partial<Species>) => {
    // TODO: Persist the newly added product to the inventory data source.
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedIds(new Set(paginatedData.map(item => item.id)));
    } else {
      setSelectedIds(new Set());
    }
  };

  const handleToggleRow = (itemId: string) => {
    setSelectedIds(prev => {
      const next = new Set(prev);
      if (next.has(itemId)) {
        next.delete(itemId);
      } else {
        next.add(itemId);
      }
      return next;
    });
  };

  const handleEdit = (item: InventoryItem) => {
    const target = inventoryOverrides[item.id] ?? item;
    setEditingItem(target);
    setEditDraft({
      name: target.name,
      category: target.category ?? '',
      stock: target.stock.toString(),
      unitPrice: target.unitPrice != null ? target.unitPrice.toString() : '',
      status: target.status,
      supplier: target.supplier ?? '',
      dateAdded: target.dateAdded ?? '',
    });
  };

  const handleEditDraftChange = <K extends keyof InventoryEditDraft>(field: K, value: InventoryEditDraft[K]) => {
    setEditDraft(prev => (prev ? { ...prev, [field]: value } : prev));
  };

  const handleEditCancel = () => {
    setEditingItem(null);
    setEditDraft(null);
  };

  const handleEditSave = () => {
    if (!editingItem || !editDraft) {
      return;
    }

    const parsedStock = Number.parseFloat(editDraft.stock);
    const normalizedStock = Number.isFinite(parsedStock) ? parsedStock : editingItem.stock;
    const trimmedUnitPrice = editDraft.unitPrice.trim();
    const parsedUnitPrice = trimmedUnitPrice ? Number.parseFloat(trimmedUnitPrice) : undefined;
    const normalizedUnitPrice = parsedUnitPrice !== undefined && Number.isFinite(parsedUnitPrice)
      ? parsedUnitPrice
      : undefined;

    const updatedItem: InventoryItem = {
      ...editingItem,
      name: editDraft.name.trim() || editingItem.name,
      category: editDraft.category || undefined,
      stock: normalizedStock,
      unitPrice: normalizedUnitPrice,
      status: editDraft.status,
      supplier: editDraft.supplier.trim() || undefined,
      dateAdded: editDraft.dateAdded.trim() || undefined,
    };

    setInventoryOverrides(prev => ({
      ...prev,
      [updatedItem.id]: updatedItem,
    }));
    handleEditCancel();
  };

  const handleView = (item: InventoryItem) => {
    const target = inventoryOverrides[item.id] ?? item;
    setViewingItem(target);
  };

  const handleArchive = (item: InventoryItem) => {
    const target = inventoryOverrides[item.id] ?? item;
    setArchiveCandidate(target);
  };

  const handleConfirmArchive = () => {
    if (!archiveCandidate) {
      return;
    }

    setArchivedIds(prev => {
      const next = new Set(prev);
      next.add(archiveCandidate.id);
      return next;
    });

    setSelectedIds(prev => {
      if (!prev.has(archiveCandidate.id)) {
        return prev;
      }
      const next = new Set(prev);
      next.delete(archiveCandidate.id);
      return next;
    });

    setArchiveCandidate(null);
  };

  const handleCancelArchive = () => {
    setArchiveCandidate(null);
  };

  const visibleSelectionCount = paginatedData.reduce(
    (count, item) => (selectedIds.has(item.id) ? count + 1 : count),
    0,
  );
  const allVisibleSelected = paginatedData.length > 0 && visibleSelectionCount === paginatedData.length;
  const isIndeterminateSelection =
    visibleSelectionCount > 0 && visibleSelectionCount < paginatedData.length;
  const viewingSnapshot = viewingItem ? inventoryOverrides[viewingItem.id] ?? viewingItem : null;
  const archiveSnapshot = archiveCandidate ? inventoryOverrides[archiveCandidate.id] ?? archiveCandidate : null;

  const getStatusChip = (status: InventoryItem['status']) => {
    const chipProps: Record<
      InventoryItem['status'],
      { color: 'success' | 'warning' | 'error'; label: string }
    > = {
      'In Stock': { color: 'success', label: 'In Stock' },
      'Low Stock': { color: 'warning', label: 'Low Stock' },
      'Out of Stock': { color: 'error', label: 'Out of Stock' },
    };

    const { color, label } = chipProps[status];

    return (
      <Chip
        size="small"
        color={color}
        label={label}
        icon={<FiberManualRecordIcon sx={{ fontSize: 10, color: 'inherit' }} />}
        sx={{
          fontWeight: 600,
          '& .MuiChip-icon': {
            ml: 0.5,
          },
        }}
        variant="outlined"
      />
    );
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', flex: 1, width: '100%' }}>
      <Stack
        direction={{ xs: 'column', md: 'row' }}
        spacing={2}
        alignItems={{ xs: 'flex-start', md: 'center' }}
        justifyContent="space-between"
        sx={{ mb: 3 }}
      >
        <Typography variant="h4" fontWeight={800} color="text.primary">
          Inventory Management
        </Typography>
        <Stack direction="row" spacing={1.5} flexWrap="wrap">
          <Button
            variant="outlined"
            startIcon={<DownloadOutlinedIcon />}
            onClick={handleExport}
            sx={{ minWidth: 140 }}
          >
            Export Data
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setIsAddModalOpen(true)}
            sx={{ minWidth: 160 }}
          >
            Add New Product
          </Button>
        </Stack>
      </Stack>

      <Paper
        variant="outlined"
        sx={{
          p: 2,
          borderRadius: 2,
          bgcolor: (theme) => (theme.palette.mode === 'light' ? 'background.paper' : 'background.default'),
          mb: 3,
        }}
      >
        <Stack direction="row" spacing={1.5} flexWrap="wrap">
          {['Product Type', 'Status', 'Supplier', 'Origin'].map(label => (
            <Button
              key={label}
              variant="outlined"
              color="inherit"
              endIcon={<ExpandMoreIcon />}
              sx={{ textTransform: 'none' }}
            >
              {label}
            </Button>
          ))}
          <Box sx={{ flexGrow: 1 }} />
          <Button
            variant="contained"
            color="primary"
            startIcon={<FilterListIcon />}
            sx={{ textTransform: 'none' }}
          >
            Filters
          </Button>
        </Stack>
      </Paper>

      <TableContainer
        component={Paper}
        variant="outlined"
        sx={{
          borderRadius: 2,
        }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  size="small"
                  checked={allVisibleSelected}
                  indeterminate={isIndeterminateSelection}
                  onChange={handleSelectAll}
                  inputProps={{ 'aria-label': 'Select all visible inventory items' }}
                />
              </TableCell>
              <TableCell padding="checkbox" />
              <TableCell>
                <Typography variant="subtitle2">Product Name</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2">Category</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2">Stock</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2">Unit Price</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2">Status</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2">Supplier</Typography>
              </TableCell>
              <TableCell>
                <Typography variant="subtitle2">Date Added</Typography>
              </TableCell>
              <TableCell align="center">
                <Typography variant="subtitle2">Actions</Typography>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {paginatedData.map(item => (
              <TableRow
                key={item.id}
                hover
                sx={{
                  '&:last-child td, &:last-child th': { borderBottom: 0 },
                }}
              >
                <TableCell padding="checkbox">
                  <Checkbox
                    size="small"
                    checked={selectedIds.has(item.id)}
                    onChange={() => handleToggleRow(item.id)}
                    onClick={(event) => event.stopPropagation()}
                    inputProps={{ 'aria-label': `Select ${item.name}` }}
                  />
                </TableCell>
                <TableCell padding="checkbox">
                  <Avatar
                    variant="rounded"
                    src={item.imageUrl || PLACEHOLDER_IMAGE}
                    alt={item.name}
                    sx={{ width: 40, height: 40 }}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontWeight={600}>
                    {item.name}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {item.category || 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {item.stock.toFixed(2)} {item.unit || 'lbs'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {item.unitPrice ? `$${item.unitPrice.toFixed(2)}` : 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell>{getStatusChip(item.status)}</TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {item.supplier || 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {item.dateAdded || 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Stack direction="row" spacing={0.5} justifyContent="center">
                    <IconButton
                      size="small"
                      color="inherit"
                      onClick={(event) => {
                        event.stopPropagation();
                        handleEdit(item);
                      }}
                    >
                      <EditOutlinedIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="inherit"
                      onClick={(event) => {
                        event.stopPropagation();
                        handleView(item);
                      }}
                    >
                      <VisibilityOutlinedIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={(event) => {
                        event.stopPropagation();
                        handleArchive(item);
                      }}
                    >
                      <ArchiveOutlinedIcon fontSize="small" />
                    </IconButton>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Stack direction="row" justifyContent="center" sx={{ mt: 3 }}>
        <Pagination
          count={Math.max(1, totalPages)}
          page={currentPage}
          onChange={(_, page) => setCurrentPage(page)}
          color="primary"
          shape="rounded"
        />
      </Stack>

      {editingItem && editDraft && (
        <Dialog open onClose={handleEditCancel} maxWidth="sm" fullWidth>
          <DialogTitle>Edit {editingItem.name}</DialogTitle>
          <DialogContent dividers>
            <Stack spacing={2} sx={{ mt: 1 }}>
              <TextField
                label="Product Name"
                value={editDraft.name}
                onChange={(event) => handleEditDraftChange('name', event.target.value)}
              />
              <TextField
                select
                label="Category"
                value={editDraft.category}
                onChange={(event) =>
                  handleEditDraftChange('category', event.target.value as InventoryEditDraft['category'])
                }
              >
                <MenuItem value="">Unspecified</MenuItem>
                <MenuItem value="Fish">Fish</MenuItem>
                <MenuItem value="Shellfish">Shellfish</MenuItem>
                <MenuItem value="Other">Other</MenuItem>
              </TextField>
              <TextField
                label={`Stock (${editingItem?.unit || 'lbs'})`}
                type="number"
                inputProps={{ min: 0, step: 0.1 }}
                value={editDraft.stock}
                onChange={(event) => handleEditDraftChange('stock', event.target.value)}
              />
              <TextField
                label="Unit Price ($)"
                type="number"
                inputProps={{ min: 0, step: 0.01 }}
                value={editDraft.unitPrice}
                onChange={(event) => handleEditDraftChange('unitPrice', event.target.value)}
              />
              <TextField
                select
                label="Status"
                value={editDraft.status}
                onChange={(event) =>
                  handleEditDraftChange('status', event.target.value as InventoryItem['status'])
                }
              >
                <MenuItem value="In Stock">In Stock</MenuItem>
                <MenuItem value="Low Stock">Low Stock</MenuItem>
                <MenuItem value="Out of Stock">Out of Stock</MenuItem>
              </TextField>
              <TextField
                label="Supplier"
                value={editDraft.supplier}
                onChange={(event) => handleEditDraftChange('supplier', event.target.value)}
              />
              <TextField
                label="Date Added"
                type="date"
                value={editDraft.dateAdded}
                onChange={(event) => handleEditDraftChange('dateAdded', event.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleEditCancel} color="inherit">
              Cancel
            </Button>
            <Button onClick={handleEditSave} variant="contained">
              Save Changes
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {viewingSnapshot && (
        <Dialog open onClose={() => setViewingItem(null)} maxWidth="sm" fullWidth>
          <DialogTitle>{viewingSnapshot.name}</DialogTitle>
          <DialogContent dividers>
            <Stack spacing={2} sx={{ mt: 1 }}>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">
                  Category
                </Typography>
                <Typography variant="body2">{viewingSnapshot.category || 'N/A'}</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">
                  Stock
                </Typography>
                <Typography variant="body2">{viewingSnapshot.stock.toFixed(2)} {viewingSnapshot.unit || 'lbs'}</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">
                  Unit Price
                </Typography>
                <Typography variant="body2">
                  {viewingSnapshot.unitPrice != null ? `$${viewingSnapshot.unitPrice.toFixed(2)}` : 'N/A'}
                </Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">
                  Status
                </Typography>
                <Typography variant="body2">{viewingSnapshot.status}</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">
                  Supplier
                </Typography>
                <Typography variant="body2">{viewingSnapshot.supplier || 'N/A'}</Typography>
              </Box>
              <Box>
                <Typography variant="subtitle2" color="text.secondary">
                  Date Added
                </Typography>
                <Typography variant="body2">{viewingSnapshot.dateAdded || 'N/A'}</Typography>
              </Box>
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setViewingItem(null)} autoFocus>
              Close
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {archiveSnapshot && (
        <Dialog open onClose={handleCancelArchive} maxWidth="xs" fullWidth>
          <DialogTitle>Archive {archiveSnapshot.name}?</DialogTitle>
          <DialogContent dividers>
            <Typography variant="body2">
              This hides the product from the inventory list until it is restored. You can re-enable it by
              updating inventory data later.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCancelArchive} color="inherit">
              Cancel
            </Button>
            <Button onClick={handleConfirmArchive} variant="contained" color="error">
              Archive
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {isAddModalOpen && <AddProductModal onClose={() => setIsAddModalOpen(false)} onSave={handleAddProduct} />}
    </Box>
  );
};

export default InventoryManagement;
