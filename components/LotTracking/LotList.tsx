import React, { useMemo } from 'react';
import {
  Box,
  Chip,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import { alpha, useTheme } from '@mui/material/styles';
import { Lot, LotMovement } from '../../types';

interface LotListProps {
  lots: Lot[];
  movements: LotMovement[];
  selectedLot: Lot | null;
  onSelectLot: (lot: Lot) => void;
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

const getStatusChip = (status: Lot['status']) => {
  switch (status) {
    case 'Available':
      return { color: 'success' as const, label: 'Available' };
    case 'In-Transit':
      return { color: 'info' as const, label: 'In Transit' };
    case 'Processed':
      return { color: 'default' as const, label: 'Processed' };
    default:
      return { color: 'warning' as const, label: status };
  }
};

export const LotList: React.FC<LotListProps> = ({ lots, movements, selectedLot, onSelectLot, searchQuery, onSearchChange }) => {
  const theme = useTheme();
  const latestMovements = useMemo(() => {
    const movementMap = new Map<string, LotMovement>();
    movements.forEach((movement) => {
      if (!movementMap.has(movement.lotId)) {
        movementMap.set(movement.lotId, movement);
      }
    });
    return movementMap;
  }, [movements]);

  return (
    <Stack spacing={3}>
      <TextField
        fullWidth
        placeholder="Search by Lot ID, Species, Origin..."
        value={searchQuery}
        onChange={(event) => onSearchChange(event.target.value)}
        InputProps={{
          startAdornment: (
            <SearchIcon fontSize="small" sx={{ color: 'text.secondary', mr: 1.5 }} />
          ),
        }}
      />

      <Stack direction="row" spacing={1.5} flexWrap="wrap">
        {['Status', 'Location', 'Species', 'Date Range'].map((filter) => (
          <Chip
            key={filter}
            icon={<FilterListIcon fontSize="small" />}
            label={`${filter}: All`}
            variant="outlined"
            sx={{
              borderRadius: 2,
              bgcolor: alpha(theme.palette.action.active, theme.palette.mode === 'dark' ? 0.08 : 0.04),
              color: 'text.secondary',
            }}
          />
        ))}
      </Stack>

      <TableContainer component={Paper} variant="outlined" sx={{ borderRadius: 2 }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>Lot ID</TableCell>
              <TableCell>Species</TableCell>
              <TableCell>Current Location</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Last Updated</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {lots.map((lot) => {
              const latestMovement = latestMovements.get(lot.id);
              const { color, label } = getStatusChip(lot.status);
              return (
                <TableRow
                  key={lot.id}
                  hover
                  selected={selectedLot?.id === lot.id}
                  sx={{ cursor: 'pointer' }}
                  onClick={() => onSelectLot(lot)}
                >
                  <TableCell>
                    <Typography variant="body2" fontWeight={700} color="primary">
                      {lot.lotId}
                    </Typography>
                  </TableCell>
                  <TableCell>{lot.species}</TableCell>
                  <TableCell>{latestMovement ? latestMovement.location : 'N/A'}</TableCell>
                  <TableCell>
                    <Chip color={color} label={label} size="small" variant={color === 'default' ? 'outlined' : 'filled'} />
                  </TableCell>
                  <TableCell>
                    {latestMovement
                      ? new Date(latestMovement.timestamp.seconds * 1000).toLocaleString()
                      : 'N/A'}
                  </TableCell>
                </TableRow>
              );
            })}
            {lots.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} sx={{ textAlign: 'center', py: 4 }}>
                  <Typography variant="body2" color="text.secondary">
                    No lots match the current filters.
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Stack>
  );
};

LotList.displayName = 'LotList';
