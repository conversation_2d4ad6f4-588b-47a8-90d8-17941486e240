import React, { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import MenuItem from '@mui/material/MenuItem';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Alert from '@mui/material/Alert';
import { addLotMovement } from '../../firebase/lotService';
import { LotMovement, LotMovementType } from '../../types';

interface AddMovementModalProps {
  lotId: string;
  onClose: () => void;
}

export const AddMovementModal: React.FC<AddMovementModalProps> = ({ lotId, onClose }) => {
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [type, setType] = useState<LotMovementType>('Arrival');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!description || !location) {
      setError('Please fill out all fields.');
      return;
    }

    const newMovement: Omit<LotMovement, 'id' | 'timestamp'> = {
      lotId,
      description,
      location,
      type,
    };

    try {
      await addLotMovement(newMovement);
      onClose();
    } catch (err) {
      setError('Failed to add movement. Please try again.');
      console.error(err);
    }
  };

  return (
    <Dialog open onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Add New Movement</DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField label="Description" value={description} onChange={(e) => setDescription(e.target.value)} fullWidth />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField label="Location" value={location} onChange={(e) => setLocation(e.target.value)} fullWidth />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField select label="Movement Type" value={type} onChange={(e) => setType(e.target.value as LotMovementType)} fullWidth>
                <MenuItem value="Arrival">Arrival</MenuItem>
                <MenuItem value="Departure">Departure</MenuItem>
                <MenuItem value="Processing">Processing</MenuItem>
                <MenuItem value="Other">Other</MenuItem>
              </TextField>
            </Grid>
          </Grid>
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} color="inherit">
            Cancel
          </Button>
          <Button type="submit" variant="contained">
            Save Movement
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
