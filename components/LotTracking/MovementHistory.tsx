import React from 'react';
import {
  <PERSON>,
  Button,
  Divider,
  Paper,
  Stack,
  Typography,
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import { alpha, useTheme } from '@mui/material/styles';
import { LotMovement } from '../../types';

interface MovementHistoryProps {
  movements: LotMovement[];
  onAddMovement: () => void;
}

const getIndicatorColor = (type: string, palette: ReturnType<typeof useTheme>['palette']) => {
  switch (type) {
    case 'Arrival':
      return palette.success.main;
    case 'Departure':
      return palette.error.main;
    default:
      return palette.grey[500];
  }
};

export const MovementHistory: React.FC<MovementHistoryProps> = ({ movements, onAddMovement }) => {
  const theme = useTheme();

  return (
    <Paper variant="outlined" sx={{ p: 3, borderRadius: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
      <Typography variant="subtitle1" fontWeight={700} color="text.primary">
        Movement History
      </Typography>
      <Box sx={{ flex: 1, overflowY: 'auto', pr: 1 }}>
        <Stack spacing={3} sx={{ position: 'relative' }}>
          <Box
            sx={{
              position: 'absolute',
              left: 12,
              top: 4,
              bottom: 8,
              width: 2,
              bgcolor: alpha(theme.palette.text.secondary, 0.2),
            }}
          />
          {movements.map((movement) => {
            const indicatorColor = getIndicatorColor(movement.type, theme.palette);
            return (
              <Stack key={movement.id} spacing={0.5} sx={{ pl: 4, position: 'relative' }}>
                <Box
                  sx={{
                    position: 'absolute',
                    left: 6,
                    top: 4,
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    border: `3px solid ${theme.palette.background.paper}`,
                    bgcolor: indicatorColor,
                  }}
                />
                <Typography variant="body1" fontWeight={600} color="text.primary">
                  {movement.description}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {new Date(movement.timestamp.seconds * 1000).toLocaleString()}
                </Typography>
                <Divider sx={{ mt: 1 }} />
              </Stack>
            );
          })}
          {movements.length === 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ pl: 4 }}>
              No movements recorded yet.
            </Typography>
          )}
        </Stack>
      </Box>
      <Button
        onClick={onAddMovement}
        variant="outlined"
        startIcon={<AddIcon />}
        sx={{ textTransform: 'none', alignSelf: 'flex-start' }}
      >
        Add New Movement
      </Button>
    </Paper>
  );
};

MovementHistory.displayName = 'MovementHistory';
