import React, { useEffect, useMemo, useState } from 'react';
import { Box, Button, Grid, Paper, Stack, Typography } from '@mui/material';
import { Lot, LotMovement } from '../../types';
import { onLotsUpdate, onAllMovementsUpdate } from '../../firebase/lotService';
import { LotList } from './LotList';
import { LotDetails } from './LotDetails';
import { AddLotModal } from './AddLotModal';

export const LotTrackingView: React.FC = () => {
  const [lots, setLots] = useState<Lot[]>([]);
  const [movements, setMovements] = useState<LotMovement[]>([]);
  const [selectedLot, setSelectedLot] = useState<Lot | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const unsubLots = onLotsUpdate(setLots);
    const unsubMovements = onAllMovementsUpdate(setMovements);
    return () => {
      unsubLots();
      unsubMovements();
    };
  }, []);

  useEffect(() => {
    if (!selectedLot && lots.length > 0) {
      setSelectedLot(lots[0]);
    }
  }, [lots, selectedLot]);

  const filteredLots = useMemo(() => {
    if (!searchQuery) {
      return lots;
    }
    return lots.filter(
      (lot) =>
        lot.lotId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        lot.species.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [lots, searchQuery]);

  return (
    <Stack spacing={4}>
      <Stack
        direction={{ xs: 'column', md: 'row' }}
        justifyContent="space-between"
        spacing={3}
        alignItems={{ xs: 'flex-start', md: 'center' }}
      >
        <Stack spacing={1.5}>
          <Typography variant="h4" fontWeight={700} color="text.primary">
            Lot Movement Tracking
          </Typography>
          <Typography variant="body1" color="text.secondary" maxWidth={480}>
            Monitor and manage the real-time location and history of seafood lots.
          </Typography>
        </Stack>
        <Button variant="contained" color="primary" onClick={() => setIsModalOpen(true)} sx={{ textTransform: 'none' }}>
          Track New Lot
        </Button>
      </Stack>

      <Grid container spacing={3}>
        <Grid item xs={12} lg={8}>
          <LotList
            lots={filteredLots}
            movements={movements}
            selectedLot={selectedLot}
            onSelectLot={setSelectedLot}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
          />
        </Grid>
        <Grid item xs={12} lg={4}>
          {selectedLot ? (
            <LotDetails lot={selectedLot} />
          ) : (
            <Paper variant="outlined" sx={{ p: 3, borderRadius: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Select a lot to see details.
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>

      {isModalOpen && <AddLotModal onClose={() => setIsModalOpen(false)} />}
    </Stack>
  );
};

LotTrackingView.displayName = 'LotTrackingView';
