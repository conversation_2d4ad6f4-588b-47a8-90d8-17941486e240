import React, { useEffect, useState } from 'react';
import {
  Box,
  Button,
  Chip,
  Grid,
  Paper,
  Stack,
  Typography,
} from '@mui/material';
import { alpha } from '@mui/material/styles';
import ShareIcon from '@mui/icons-material/Share';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import { Lot, LotMovement } from '../../types';
import { onLotMovementsUpdate } from '../../firebase/lotService';
import { MovementHistory } from './MovementHistory';
import { AddMovementModal } from './AddMovementModal';

interface LotDetailsProps {
  lot: Lot;
}

const getStatusChip = (status: Lot['status']) => {
  switch (status) {
    case 'Available':
      return { color: 'success' as const, label: 'Available' };
    case 'In-Transit':
      return { color: 'info' as const, label: 'In Transit' };
    case 'Processed':
      return { color: 'default' as const, label: 'Processed' };
    default:
      return { color: 'warning' as const, label: status };
  }
};

export const LotDetails: React.FC<LotDetailsProps> = ({ lot }) => {
  const [movements, setMovements] = useState<LotMovement[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    if (!lot.id) return;
    const unsubscribe = onLotMovementsUpdate(lot.id, setMovements);
    return () => unsubscribe();
  }, [lot.id]);

  const currentStatus = movements.length > 0 ? movements[0] : null;
  const { color, label } = getStatusChip(lot.status);

  return (
    <Stack spacing={3}>
      <Paper variant="outlined" sx={{ p: 3, borderRadius: 2 }}>
        <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
          <Typography variant="h6" fontWeight={700} color="text.primary">
            Details for Lot #{lot.lotId}
          </Typography>
          <Button variant="outlined" size="small" startIcon={<ShareIcon fontSize="small" />} sx={{ textTransform: 'none' }}>
            Share
          </Button>
        </Stack>
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <Typography variant="body2" color="text.secondary">
              Species
            </Typography>
            <Typography variant="body1" fontWeight={600}>
              {lot.species}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="body2" color="text.secondary">
              Origin
            </Typography>
            <Typography variant="body1" fontWeight={600}>
              {lot.origin}
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="body2" color="text.secondary">
              Initial Weight
            </Typography>
            <Typography variant="body1" fontWeight={600}>
              {lot.initialWeight} lbs
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6}>
            <Typography variant="body2" color="text.secondary">
              Harvest Date
            </Typography>
            <Typography variant="body1" fontWeight={600}>
              {lot.harvestDate}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {currentStatus && (
        <Paper variant="outlined" sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="subtitle1" fontWeight={700} color="text.primary" gutterBottom>
            Current Status
          </Typography>
          <Stack direction="row" spacing={2.5} alignItems="center">
            <Box
              sx={{
                width: 48,
                height: 48,
                borderRadius: '50%',
                bgcolor: (theme) => alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.2 : 0.12),
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'primary.main',
              }}
            >
              <WarehouseIcon />
            </Box>
            <Stack spacing={0.5}>
              <Typography variant="body1" fontWeight={600}>
                {currentStatus.location}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Last Update: {new Date(currentStatus.timestamp.seconds * 1000).toLocaleString()}
              </Typography>
              <Chip color={color} label={label} size="small" variant={color === 'default' ? 'outlined' : 'filled'} sx={{ alignSelf: 'flex-start' }} />
            </Stack>
          </Stack>
        </Paper>
      )}

      <MovementHistory movements={movements} onAddMovement={() => setIsModalOpen(true)} />

      {isModalOpen && <AddMovementModal lotId={lot.id} onClose={() => setIsModalOpen(false)} />}
    </Stack>
  );
};

LotDetails.displayName = 'LotDetails';
