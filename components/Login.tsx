import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Divider,
  Paper,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import { signInWithEmailAndPassword, signInWithPopup } from 'firebase/auth';
import React from 'react';
import { auth, googleProvider } from '../firebase/config';
import { alpha } from '@mui/material/styles';

interface LoginProps {
  onLoginSuccess: () => void;
}

export const Login: React.FC<LoginProps> = ({ onLoginSuccess }) => {
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [error, setError] = React.useState('');
  const [loading, setLoading] = React.useState(false);

  const handleGoogleSignIn = async () => {
    setLoading(true);
    setError('');
    try {
      await signInWithPopup(auth, googleProvider);
      onLoginSuccess();
    } catch (err: any) {
      setError(err.message || 'Failed to sign in with Google');
      console.error('Google sign-in error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      await signInWithEmailAndPassword(auth, email, password);
      onLoginSuccess();
    } catch (err: any) {
      setError(err.message || 'Failed to sign in');
      console.error('Email sign-in error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundImage: (theme) =>
          theme.palette.mode === 'light'
            ? 'linear-gradient(135deg, #eff6ff 0%, #e0e7ff 100%)'
            : 'linear-gradient(135deg, #0f172a 0%, #1f2937 100%)',
        px: 2,
      }}
    >
      <Paper
        elevation={12}
        sx={{
          maxWidth: 420,
          width: '100%',
          borderRadius: 4,
          px: 5,
          py: 6,
          backdropFilter: 'blur(12px)',
          WebkitBackdropFilter: 'blur(12px)',
          backgroundColor: (theme) =>
            alpha(
              theme.palette.mode === 'light' ? theme.palette.background.paper : theme.palette.background.default,
              theme.palette.mode === 'light' ? 0.85 : 0.55,
            ),
          border: (theme) =>
            `1px solid ${alpha(
              theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white,
              theme.palette.mode === 'light' ? 0.08 : 0.16,
            )}`,
        }}
      >
        <Stack spacing={3}>
          <Box>
            <Typography variant="h4" fontWeight={800} textAlign="center">
              HACCP Helper
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center" mt={1}>
              Sign in to manage your seafood inventory
            </Typography>
          </Box>

          {error && <Alert severity="error">{error}</Alert>}

          <Button
            fullWidth
            size="large"
            variant="outlined"
            onClick={handleGoogleSignIn}
            disabled={loading}
            startIcon={
              <Box
                component="span"
                sx={{
                  display: 'inline-flex',
                  '& svg': { height: 20, width: 20 },
                }}
              >
                <svg viewBox="0 0 24 24">
                  <path
                    fill="#4285F4"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="#34A853"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="#EA4335"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
              </Box>
            }
            sx={{
              bgcolor: 'background.paper',
              borderWidth: 2,
              borderColor: (theme) => (theme.palette.mode === 'light' ? 'grey.300' : 'grey.700'),
              color: 'text.primary',
              textTransform: 'none',
              fontWeight: 600,
              '&:hover': {
                bgcolor: (theme) => (theme.palette.mode === 'light' ? 'grey.50' : 'grey.900'),
                borderColor: (theme) => (theme.palette.mode === 'light' ? 'grey.400' : 'grey.600'),
              },
            }}
          >
            {loading ? 'Signing in...' : 'Continue with Google'}
          </Button>

          <Divider>Or sign in with email</Divider>

          <Box component="form" onSubmit={handleEmailSignIn}>
            <Stack spacing={2.5}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                autoComplete="email"
              />
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                autoComplete="current-password"
              />
              <Button
                type="submit"
                variant="contained"
                size="large"
                fullWidth
                disabled={loading}
                sx={{ textTransform: 'none', fontWeight: 600 }}
              >
                {loading ? 'Signing in...' : 'Sign In'}
              </Button>
            </Stack>
          </Box>

          <Typography variant="body2" color="text.secondary" textAlign="center">
            Contact your administrator for account access
          </Typography>
        </Stack>
      </Paper>
    </Box>
  );
};
