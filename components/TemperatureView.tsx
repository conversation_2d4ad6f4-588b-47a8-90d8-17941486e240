import React, { useCallback, useEffect, useState } from 'react';
import {
    Alert,
    Box,
    Button,
    CircularProgress,
    Grid,
    Paper,
    Stack,
    Typography,
} from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';
import type { Theme } from '@mui/material/styles';
import { TempStickSensor } from '../types';
import { ThermometerIcon } from './IconComponents';
import { TemperatureChart } from './TemperatureChart';
import { fetchSensors } from '../services/tempstickService';
import { MockDataWarning } from './MockDataWarning';

const timeSince = (date: Date): string => {
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return `${Math.floor(interval)} years ago`;
    interval = seconds / 2592000;
    if (interval > 1) return `${Math.floor(interval)} months ago`;
    interval = seconds / 86400;
    if (interval > 1) return `${Math.floor(interval)} days ago`;
    interval = seconds / 3600;
    if (interval > 1) return `${Math.floor(interval)} hours ago`;
    interval = seconds / 60;
    if (interval > 1) return `${Math.floor(interval)} minutes ago`;
    return `${Math.floor(seconds)} seconds ago`;
};

const getTemperatureStyles = (temp: number, theme: Theme) => {
    if (temp > 140) {
        return {
            borderColor: theme.palette.success.main,
            accentColor: theme.palette.success.main,
            background: alpha(theme.palette.success.main, theme.palette.mode === 'dark' ? 0.25 : 0.12),
        };
    }
    if (temp > 40) {
        return {
            borderColor: theme.palette.error.main,
            accentColor: theme.palette.error.main,
            background: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.25 : 0.12),
        };
    }
    if (temp > 32) {
        return {
            borderColor: theme.palette.warning.main,
            accentColor: theme.palette.warning.main,
            background: alpha(theme.palette.warning.main, theme.palette.mode === 'dark' ? 0.25 : 0.12),
        };
    }
    return {
        borderColor: theme.palette.info.main,
        accentColor: theme.palette.info.main,
        background: alpha(theme.palette.info.main, theme.palette.mode === 'dark' ? 0.25 : 0.12),
    };
};

export const TemperatureView: React.FC = () => {
    const theme = useTheme();
    const [sensors, setSensors] = useState<TempStickSensor[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
    const [viewingSensor, setViewingSensor] = useState<TempStickSensor | null>(null);

    const loadSensors = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const data = await fetchSensors();
            setSensors(data);
            setLastUpdated(new Date());
        } catch (fetchError) {
            console.error(fetchError);
            setError(
                'Failed to load sensor data. This is a mock service; if you see this, there is an issue in the mock implementation.',
            );
            setSensors([]);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        loadSensors();
        const intervalId = window.setInterval(() => {
            loadSensors();
        }, 300000);
        return () => window.clearInterval(intervalId);
    }, [loadSensors]);

    return (
        <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Paper variant="outlined" sx={{ p: { xs: 3, md: 4 }, borderRadius: 3 }}>
                <Stack
                    direction={{ xs: 'column', sm: 'row' }}
                    spacing={2}
                    justifyContent="space-between"
                    alignItems={{ xs: 'flex-start', sm: 'center' }}
                >
                    <Stack direction="row" spacing={2} alignItems="center">
                        <Box
                            sx={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: 48,
                                height: 48,
                                borderRadius: 2,
                                bgcolor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.35 : 0.12),
                                color: theme.palette.error.main,
                                '& svg': { width: 24, height: 24 },
                            }}
                        >
                            <ThermometerIcon />
                        </Box>
                        <Typography variant="h4" fontWeight={700} color="text.primary">
                            Temperature Monitoring
                        </Typography>
                    </Stack>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={loadSensors}
                        disabled={loading}
                        sx={{ textTransform: 'none' }}
                    >
                        {loading ? 'Refreshing…' : 'Refresh'}
                    </Button>
                </Stack>
                <Box sx={{ mt: 2 }}>
                    <MockDataWarning />
                </Box>
                {error && (
                    <Alert severity="error" sx={{ mt: 2 }}>
                        {error}
                    </Alert>
                )}
                {loading && !error && (
                    <Box sx={{ display: 'grid', placeItems: 'center', py: 6 }}>
                        <CircularProgress size={24} />
                    </Box>
                )}
                {!loading && !error && (
                    <>
                        <Grid container spacing={3} sx={{ mt: 1 }}>
                            {sensors.map((sensor) => {
                                const temperatureStyles = getTemperatureStyles(sensor.last_temp, theme);
                                return (
                                    <Grid item xs={12} md={6} lg={4} key={sensor.sensor_id}>
                                        <Paper
                                            variant="outlined"
                                            role="button"
                                            tabIndex={0}
                                            onClick={() => setViewingSensor(sensor)}
                                            onKeyPress={(event) => {
                                                if (event.key === 'Enter' || event.key === ' ') {
                                                    setViewingSensor(sensor);
                                                }
                                            }}
                                            sx={{
                                                p: 2.5,
                                                borderRadius: 2,
                                                cursor: 'pointer',
                                                transition: theme.transitions.create(['box-shadow', 'transform']),
                                                borderLeft: `4px solid ${temperatureStyles.borderColor}`,
                                                '&:hover': {
                                                    boxShadow: theme.shadows[6],
                                                    transform: 'translateY(-2px)',
                                                },
                                                bgcolor: temperatureStyles.background,
                                            }}
                                        >
                                            <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
                                                <Typography variant="h6" fontWeight={700} color="text.primary">
                                                    {sensor.sensor_name}
                                                </Typography>
                                                <Box textAlign="right">
                                                    <Typography
                                                        variant="h4"
                                                        fontWeight={800}
                                                        sx={{ color: temperatureStyles.accentColor }}
                                                    >
                                                        {sensor.last_temp.toFixed(1)}°{sensor.temp_f_c?.toUpperCase() || 'F'}
                                                    </Typography>
                                                    <Typography variant="body2" color="text.secondary">
                                                        {sensor.last_humidity.toFixed(1)}% RH
                                                    </Typography>
                                                </Box>
                                            </Stack>
                                            <Stack spacing={0.5} sx={{ mt: 2, pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
                                                <Typography variant="caption" color="text.secondary">
                                                    <strong>Battery:</strong> {sensor.battery_pct}%
                                                </Typography>
                                                <Typography variant="caption" color="text.secondary">
                                                    <strong>Signal:</strong>{' '}
                                                    {sensor.rssi < 0 ? `${sensor.rssi} dBm` : `${sensor.rssi}%`}
                                                </Typography>
                                                <Typography variant="caption" color="text.secondary">
                                                    <strong>Last Update:</strong> {timeSince(new Date(sensor.last_checkin))}
                                                </Typography>
                                            </Stack>
                                        </Paper>
                                    </Grid>
                                );
                            })}
                            {sensors.length === 0 && (
                                <Grid item xs={12}>
                                    <Paper variant="outlined" sx={{ p: 4, textAlign: 'center', borderRadius: 2 }}>
                                        <Typography variant="body2" color="text.secondary">
                                            No sample sensors available.
                                        </Typography>
                                    </Paper>
                                </Grid>
                            )}
                        </Grid>
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center', mt: 3 }}>
                            Last refreshed: {lastUpdated ? lastUpdated.toLocaleTimeString() : 'N/A'}
                        </Typography>
                    </>
                )}
            </Paper>

            {viewingSensor && (
                <TemperatureChart sensor={viewingSensor} onClose={() => setViewingSensor(null)} />
            )}
        </Box>
    );
};

TemperatureView.displayName = 'TemperatureView';
