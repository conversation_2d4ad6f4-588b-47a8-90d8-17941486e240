
import Grid from '@mui/material/Grid';
import { Avatar, Box, Paper, Stack, Typography } from '@mui/material';
import React, { useMemo } from 'react';
import { HACCPEvent } from '../types';
import { CubeIcon } from './IconComponents';

interface InventoryDisplayProps {
  events: HACCPEvent[];
}

export const InventoryDisplay: React.FC<InventoryDisplayProps> = ({ events }) => {
  const inventory = useMemo(() => {
    const inventoryMap: { [product: string]: number } = {};

    // Sort events from oldest to newest to calculate inventory chronologically
    [...events].sort((a, b) => {
        const timeA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const timeB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return timeA - timeB;
    }).forEach(event => {
        if (!event.product || typeof event.quantity !== 'number') {
            return;
        }

        if (!inventoryMap[event.product]) {
            inventoryMap[event.product] = 0;
        }

        switch (event.eventType) {
            case 'receiving':
                inventoryMap[event.product] += event.quantity;
                break;
            case 'sales':
            case 'disposal':
                inventoryMap[event.product] -= event.quantity;
                break;
            default:
                break;
        }
    });

    // Filter out items with zero or negative quantity and format for display
    return Object.entries(inventoryMap)
        .filter(([, quantity]) => quantity > 0.01) // Use an epsilon for float comparison
        .map(([product, quantity]) => ({
            product,
            quantity,
        }))
        .sort((a, b) => a.product.localeCompare(b.product));
  }, [events]);

  return (
    <Box component="section">
      <Stack direction="row" alignItems="center" spacing={2} mb={3}>
        <Avatar
          variant="rounded"
          sx={{
            bgcolor: (theme) => theme.palette.success.light,
            color: (theme) => theme.palette.success.dark,
            width: 40,
            height: 40,
          }}
        >
          <CubeIcon className="h-5 w-5" />
        </Avatar>
        <Typography variant="h5" fontWeight={700} color="text.primary">
          Inventory Summary
        </Typography>
      </Stack>

      {inventory.length === 0 ? (
        <Paper
          variant="outlined"
          sx={{
            textAlign: 'center',
            px: 3,
            py: 6,
            bgcolor: (theme) => (theme.palette.mode === 'light' ? 'grey.50' : 'background.default'),
          }}
        >
          <Typography variant="body2" color="text.secondary">
            No inventory data available. Record a &quot;Receiving&quot; event to get started.
          </Typography>
        </Paper>
      ) : (
        <Grid container spacing={2}>
          {inventory.map(({ product, quantity }) => (
            <Grid item key={product} xs={12} sm={6} md={4} lg={3} xl={2}>
              <Paper
                variant="outlined"
                sx={{
                  textAlign: 'center',
                  px: 2.5,
                  py: 3,
                  borderRadius: 2,
                  bgcolor: (theme) => (theme.palette.mode === 'light' ? 'grey.50' : 'background.paper'),
                }}
              >
                <Typography variant="subtitle1" fontWeight={600} color="text.primary" noWrap>
                  {product}
                </Typography>
                <Typography variant="h4" fontWeight={700} color="primary.main">
                  {quantity.toFixed(2)}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  lbs
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      )}
    </Box>
  );
};
