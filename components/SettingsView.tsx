import React from 'react';
import { Box, Button, Grid, Paper, Typography } from '@mui/material';
import { alpha, useTheme } from '@mui/material/styles';

type OldViews = 'vendors' | 'species' | 'locations' | 'orders' | 'temperature';

interface SettingsViewProps {
    onNavigate: (view: OldViews) => void;
}

const ACTIONS: Array<{ label: string; view: OldViews }> = [
    { label: 'Manage Vendors', view: 'vendors' },
    { label: 'Manage Species', view: 'species' },
    { label: 'Manage Locations', view: 'locations' },
    { label: 'Manage Orders', view: 'orders' },
    { label: 'Temperature Logs', view: 'temperature' },
];

export const SettingsView: React.FC<SettingsViewProps> = ({ onNavigate }) => {
    const theme = useTheme();

    return (
        <Paper variant="outlined" sx={{ p: { xs: 3, md: 4 }, borderRadius: 3, width: '100%' }}>
            <Typography variant="h4" fontWeight={700} color="text.primary" gutterBottom>
                Settings
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                Access domain management tools and configuration areas below.
            </Typography>

            <Grid container spacing={2.5}>
                {ACTIONS.map((action) => (
                    <Grid item xs={12} sm={6} lg={4} key={action.view}>
                        <Button
                            fullWidth
                            variant="outlined"
                            onClick={() => onNavigate(action.view)}
                            sx={{
                                justifyContent: 'flex-start',
                                textTransform: 'none',
                                fontWeight: 600,
                                borderRadius: 2,
                                p: 2.5,
                                borderColor: alpha(theme.palette.primary.main, 0.4),
                                '&:hover': {
                                    backgroundColor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.25 : 0.08),
                                    borderColor: theme.palette.primary.main,
                                },
                            }}
                        >
                            {action.label}
                        </Button>
                    </Grid>
                ))}
            </Grid>
        </Paper>
    );
};

SettingsView.displayName = 'SettingsView';
