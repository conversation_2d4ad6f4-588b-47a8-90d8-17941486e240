# Project Overview

This is a web application designed to assist with seafood inventory management and HACCP (Hazard Analysis and Critical Control Points) compliance. The application is built using React and Vite, with Firebase for the backend (Firestore for database, Storage for images, and Authentication). It also integrates with the Google Gemini API for voice commands and AI-powered image analysis.

The application allows users to:
- Log various HACCP events, such as receiving, sales, disposal, and sanitation.
- Manage inventory, including product details, quantities, and locations.
- Track purchase orders.
- View reports and a calendar of events.
- Use voice commands to interact with the application.
- Analyze images of products for quality control.

# Building and Running

**Prerequisites:**

*   Node.js
*   pnpm

**Running the application:**

1.  **Install dependencies:**
    ```bash
    pnpm install
    ```

2.  **Set up environment variables:**
    Create a `.env.local` file and add your Gemini API key:
    ```
    VITE_GEMINI_API_KEY=your_api_key
    ```

3.  **Run the development server:**
    ```bash
    pnpm run dev
    ```
    The application will be available at `http://localhost:3000`.

**Building for production:**

```bash
pnpm run build
```

# Development Conventions

*   **Framework:** The project uses React with Vite for the frontend.
*   **Language:** TypeScript is used for type safety.
*   **Styling:** The project appears to use a utility-first CSS framework like Tailwind CSS, based on the class names in `App.tsx`.
*   **Backend:** Firebase is used for the database, storage, and authentication.
*   **API:** The Google Gemini API is used for voice commands and image analysis.
*   **Component-Based Architecture:** The application is structured into reusable React components, located in the `components` directory.
*   **State Management:** Component-level state is managed with React hooks (`useState`, `useEffect`, etc.).
*   **File Naming:** Components are named using PascalCase (e.g., `AddProductModal.tsx`).
