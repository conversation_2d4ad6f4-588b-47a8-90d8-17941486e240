import { TempStickSensor, TempStickReading } from '../types';

// --- Mock Data ---

// Generates a realistic array of sensor data.
const mockSensors: TempStickSensor[] = [
  {
    sensor_id: "mock_1",
    sensor_name: "Walk-in Freezer",
    last_temp: -5.2,
    last_humidity: 45.1,
    last_checkin: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
    battery_pct: 88,
    rssi: -65,
    temp_f_c: "f"
  },
  {
    sensor_id: "mock_2",
    sensor_name: "Reach-in Cooler",
    last_temp: 38.5,
    last_humidity: 60.3,
    last_checkin: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
    battery_pct: 95,
    rssi: -72,
    temp_f_c: "f"
  },
  {
    sensor_id: "mock_3",
    sensor_name: "Display Case",
    last_temp: 33.1,
    last_humidity: 55.8,
    last_checkin: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
    battery_pct: 71,
    rssi: -80,
    temp_f_c: "f"
  },
   {
    sensor_id: "mock_4",
    sensor_name: "Hot Holding",
    last_temp: 142.7,
    last_humidity: 30.2,
    last_checkin: new Date(Date.now() - 3 * 60 * 1000).toISOString(), // 3 minutes ago
    battery_pct: 99,
    rssi: -60,
    temp_f_c: "f"
  }
];

// Generates a time series of temperature readings for the chart.
const generateMockReadings = (timeRange: string): TempStickReading[] => {
    const readings: TempStickReading[] = [];
    const now = new Date();
    let startTime: Date;
    let points = 100;

    switch (timeRange) {
        case '24_hours':
            startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
        case 'last_week':
            startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            points = 168;
            break;
        case 'last_month':
            startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            points = 300;
            break;
        case 'today':
        default:
            startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
    }
    
    const timeDiff = now.getTime() - startTime.getTime();

    for (let i = 0; i < points; i++) {
        const timestamp = new Date(startTime.getTime() + (timeDiff * i) / points);
        const temperature = -5 + Math.random() * 2 - Math.sin(i / 10) * 1.5; // Simulate freezer temp fluctuation
        const humidity = 45 + Math.random() * 5;
        readings.push({
            sensor_time: timestamp.toISOString(),
            temperature: parseFloat(temperature.toFixed(2)),
            humidity: parseFloat(humidity.toFixed(2)),
        });
    }

    return readings;
};


// --- Service Functions ---

/**
 * Fetches the list of all sensors.
 * This is a mock implementation and returns sample data after a short delay.
 * In a real application, this function would make a fetch call to '/api/tempstick/sensors/all'.
 */
export const fetchSensors = (): Promise<TempStickSensor[]> => {
    console.log("Mock Service: Fetching sensors...");
    return new Promise(resolve => {
        setTimeout(() => {
            resolve(mockSensors);
        }, 800); // Simulate network latency
    });
};

/**
 * Fetches historical readings for a specific sensor and time range.
 * This is a mock implementation and returns generated sample data.
 * In a real application, this would fetch from '/api/tempstick/sensor/<id>/readings?setting=<timeRange>'.
 */
export const fetchReadings = (sensorId: string, timeRange: string): Promise<TempStickReading[]> => {
    console.log(`Mock Service: Fetching readings for sensor ${sensorId} over ${timeRange}...`);
    return new Promise(resolve => {
        setTimeout(() => {
            const data = generateMockReadings(timeRange);
            resolve(data);
        }, 1200); // Simulate network latency
    });
};
